import android.content.Intent
import androidx.test.espresso.Espresso
import androidx.test.espresso.action.ViewActions
import androidx.test.espresso.matcher.ViewMatchers.withId
import androidx.test.rule.ActivityTestRule
import androidx.test.runner.AndroidJUnit4
import com.dateup.android.activity.TestActivity
import com.dateup.android.geofire.NearByUsers
import com.dateup.android.viewModels.BrowseProfilesViewModel
import com.google.firebase.auth.FirebaseAuth
import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import org.junit.Assert
import org.junit.Before
import org.junit.Rule
import org.junit.Test
import org.junit.runner.RunWith
import android.R
import android.os.Looper
import androidx.annotation.UiThread
import androidx.test.espresso.IdlingRegistry
import androidx.test.espresso.idling.CountingIdlingResource
import java.util.concurrent.CountDownLatch
import java.util.concurrent.TimeUnit
import kotlin.concurrent.thread
import android.view.LayoutInflater
import androidx.test.InstrumentationRegistry.getTargetContext
import androidx.test.platform.app.InstrumentationRegistry
import com.google.android.gms.common.util.CollectionUtils
import org.junit.rules.Timeout


@RunWith(AndroidJUnit4::class)
class BrowseProfilesViewModelTest {

    private lateinit var viewModel: BrowseProfilesViewModel
    private val countDownLatch = CountDownLatch(1)

    var searchLat = 37.7506751
    var searchLong = -121.8991087
    var radius = 5000
    var mainUserId = "rOpEYdBk48Pxn0YL2y6larsQzTl1"

    @get:Rule
    var activityRule: ActivityTestRule<TestActivity> =
        ActivityTestRule(TestActivity::class.java)

    @Before
    fun setup() {
        activityRule.launchActivity(Intent())
        viewModel = BrowseProfilesViewModel()
    }

    private fun getUsersTest() {
        val restApiUsersMap = HashMap<String, TestUser>()
        val localGeofireUsersMap = HashMap<String, TestUser>()

        viewModel.getNearByUsersForTesting(
            searchLat,
            searchLong,
            radius,
            mainUserId,
        ) { restApiUsersList ->
            println("GetUsers -------------------------------------------------------------")
            println("GetUsers users size from rest api: ${restApiUsersList?.size}")

            if (restApiUsersList != null) {
                for (user in restApiUsersList) {
                    restApiUsersMap[user.uid.toString()] = TestUser(user.name.toString(), user.dob.toString(), user.gender.toString(), user.city.toString())
                }
            }

            if (radius == 100) {
                radius = 3000
            }

            viewModel.readNearByUsersFromFirebaseForTesting(
                searchLat,
                searchLong,
                radius,
                false,
                mainUserId
            ) { androidGeofireLibUsersMembersMap, androidGeofireLibUsersGuestsMap ->
                println("GetUsers users size from android geofire lib: ${androidGeofireLibUsersGuestsMap?.size?.let {
                    androidGeofireLibUsersMembersMap?.size?.plus(
                        it
                    )
                }}")

                if (!androidGeofireLibUsersMembersMap.isNullOrEmpty()) {
                    for ((key, value) in androidGeofireLibUsersMembersMap) {
                        localGeofireUsersMap[key] = TestUser(value?.name.toString(), value?.dob.toString(),
                            value?.gender.toString(), value?.city.toString())
                    }
                }

                if (!androidGeofireLibUsersGuestsMap.isNullOrEmpty()) {
                    for ((key, value) in androidGeofireLibUsersGuestsMap) {
                        localGeofireUsersMap[key] = TestUser(value?.name.toString(), value?.dob.toString(),
                            value?.gender.toString(), value?.city.toString())
                    }
                }

                countDownLatch.countDown()

                println("GetUsers restApiUsersMap size: ${restApiUsersMap.size}")
                println("GetUsers localGeofireUsersMap size: ${localGeofireUsersMap.size}")

                if (restApiUsersMap.size != localGeofireUsersMap.size) {
                    val unCommonKeys = mutableListOf<String>()
                    restApiUsersMap.forEach { (key, _) ->
                        if (!localGeofireUsersMap.containsKey(key)) {
                            unCommonKeys.add(key)
                        }
                    }

                    localGeofireUsersMap.forEach{ (key, _) ->
                        if (!restApiUsersMap.containsKey(key)) {
                            unCommonKeys.add(key)
                        }
                    }

                    println("GetUsers uncommon keys size: ${unCommonKeys.size}")
                    println("GetUsers uncommon keys: $unCommonKeys")
                }

                Assert.assertEquals(restApiUsersMap.size, localGeofireUsersMap.size)
                Assert.assertEquals(restApiUsersMap, localGeofireUsersMap)
            }
        }
        countDownLatch.await()
    }

    @Test
    fun getUsersTestSeattle() {
        searchLat = 47.6062095
        searchLong = -122.3320708
        radius = 100
        mainUserId = "rOpEYdBk48Pxn0YL2y6larsQzTl1"
        getUsersTest()
    }

    @Test
    fun getUsersTestChicago() {
        searchLat = 41.882925
        searchLong = -87.622609
        radius = 200
        mainUserId = "rOpEYdBk48Pxn0YL2y6larsQzTl1"
        getUsersTest()
    }

    @Test
    fun getUsersTestNewYork() {
        searchLat = 40.71172273662094
        searchLong = -73.98932580714919
        radius = 25
        mainUserId = "rOpEYdBk48Pxn0YL2y6larsQzTl1"
        getUsersTest()
    }

    @Test
    fun getUsersTestSFORadius25() {
        searchLat = 37.7749295
        searchLong = -122.4194155
        radius = 25
        mainUserId = "rOpEYdBk48Pxn0YL2y6larsQzTl1"
        getUsersTest()
    }

    @Test
    fun getUsersTestSFORadius50() {
        searchLat = 37.7749295
        searchLong = -122.4194155
        radius = 50
        mainUserId = "rOpEYdBk48Pxn0YL2y6larsQzTl1"
        getUsersTest()
    }

    @Test
    fun getUsersTestSFORadius100() {
        searchLat = 37.7749295
        searchLong = -122.4194155
        radius = 100
        mainUserId = "rOpEYdBk48Pxn0YL2y6larsQzTl1"
        getUsersTest()
    }

    @Test
    fun getUsersTestSFORadius500() {
        searchLat = 37.7749295
        searchLong = -122.4194155
        radius = 500
        mainUserId = "rOpEYdBk48Pxn0YL2y6larsQzTl1"
        getUsersTest()
    }

    @Test
    fun getUsersTestSFORadius1000() {
        searchLat = 37.7749295
        searchLong = -122.4194155
        radius = 1000
        mainUserId = "rOpEYdBk48Pxn0YL2y6larsQzTl1"
        getUsersTest()
    }

    @Test
    fun getUsersTestSFORadius1500() {
        searchLat = 37.7749295
        searchLong = -122.4194155
        radius = 1500
        mainUserId = "rOpEYdBk48Pxn0YL2y6larsQzTl1"
        getUsersTest()
    }

    @Test
    fun getUsersTestSFORadius2000() {
        searchLat = 37.7749295
        searchLong = -122.4194155
        radius = 2000
        mainUserId = "rOpEYdBk48Pxn0YL2y6larsQzTl1"
        getUsersTest()
    }

    @Test
    fun getUsersTestSFORadius5000() {
        searchLat = 37.7749295
        searchLong = -122.4194155
        radius = 5000
        mainUserId = "rOpEYdBk48Pxn0YL2y6larsQzTl1"
        getUsersTest()
    }
}

data class TestUser(val name: String, val dob: String, val gender: String, val city: String)





