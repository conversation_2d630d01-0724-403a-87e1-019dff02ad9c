<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:keepScreenOn="true">

  <androidx.constraintlayout.widget.ConstraintLayout
      android:id="@+id/relativeLayout"
      android:layout_width="match_parent"
      android:layout_height="match_parent"
      android:layout_gravity="center_horizontal"
      android:layout_marginTop="85dp"
      android:elevation="1dp">

    <ImageView
        android:id="@+id/dl_sample"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="36dp"
        android:layout_marginEnd="36dp"
        android:layout_marginTop="12dp"
        android:scaleType="centerCrop"
        android:src="@drawable/ic_license_image"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/scan_info_text_view"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="24dp"
        android:layout_marginTop="24dp"
        android:layout_marginEnd="24dp"
        app:layout_constraintTop_toBottomOf="@+id/dl_sample"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:fontFamily="@font/font_nunitosans_extrabold"
        android:gravity="center"
        android:text="@string/height_scan_desc"
        android:textColor="@color/grey1"
        android:textSize="18sp" />

    <com.dateup.android.heightVerification.camera.CameraSourcePreview
        android:id="@+id/camera_preview"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginTop="36dp"
        android:background="@color/black"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintBottom_toTopOf="@+id/help_text_view"
        app:layout_constraintTop_toBottomOf="@+id/scan_info_text_view">

      <include layout="@layout/camera_preview_overlay" />

    </com.dateup.android.heightVerification.camera.CameraSourcePreview>

    <TextView
        android:id="@+id/help_text_view"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="24dp"
        android:layout_marginEnd="24dp"
        android:fontFamily="@font/font_nunitosans_bold"
        android:gravity="center"
        android:text="Help"
        android:textColor="@color/color_primary"
        android:textSize="18sp"
        android:layout_marginTop="24dp"
        android:layout_marginBottom="24dp"
        app:layout_constraintTop_toBottomOf="@+id/camera_preview"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"/>

    <include
        android:id="@+id/progress"
        layout="@layout/dateup_progress_bar"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"/>
  </androidx.constraintlayout.widget.ConstraintLayout>

  <include
      android:id="@+id/top_action_bar"
      layout="@layout/top_action_bar_in_live_camera"
      android:layout_width="match_parent"
      android:layout_height="wrap_content"
      android:layout_gravity="top"/>

</androidx.coordinatorlayout.widget.CoordinatorLayout>
