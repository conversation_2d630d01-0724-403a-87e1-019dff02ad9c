<androidx.constraintlayout.widget.ConstraintLayout
	xmlns:android="http://schemas.android.com/apk/res/android"
	xmlns:app="http://schemas.android.com/apk/res-auto"
	xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
	android:layout_height="match_parent"
	android:background="@color/education1_activity_education1_constraint_layout_background_color">

	<include
		android:id="@+id/custom_action_bar"
		layout="@layout/custom_action_bar"
		android:layout_width="match_parent"
		android:layout_height="wrap_content" />

	<androidx.constraintlayout.widget.ConstraintLayout
		android:id="@+id/button_constraint_layout"
		android:layout_width="0dp"
		android:layout_height="@dimen/education1_activity_button_constraint_layout_height"
		android:layout_marginStart="@dimen/education1_activity_button_constraint_layout_margin_start"
		android:layout_marginTop="36dp"
		android:layout_marginEnd="@dimen/education1_activity_button_constraint_layout_margin_end"
		app:layout_constraintLeft_toLeftOf="parent"
		app:layout_constraintRight_toRightOf="parent"
		app:layout_constraintTop_toBottomOf="@+id/group_constraint_layout">

		<android.widget.Button
			android:id="@+id/button_enable_location"
			style="?android:attr/borderlessButtonStyle"
			android:theme="@style/BottomCTAButton"
			android:layout_width="0dp"
			android:layout_height="@dimen/education1_activity_button_large_active_button_height"
			android:layout_marginEnd="@dimen/education1_activity_button_large_active_button_margin_end"
			android:background="@drawable/phone_number_activity_button_large_active_button_selector"
			android:text="@string/education1_activity_button_large_active_button_text"
			android:textColor="@color/education1_activity_button_large_active_button_text_color"
			app:layout_constraintBottom_toBottomOf="parent"
			app:layout_constraintLeft_toLeftOf="parent"
			app:layout_constraintRight_toRightOf="parent"
			app:layout_constraintTop_toTopOf="parent"
			tools:layout_editor_absoluteX="0dp"
			tools:layout_editor_absoluteY="0dp" />
	</androidx.constraintlayout.widget.ConstraintLayout>

	<androidx.constraintlayout.widget.ConstraintLayout
		android:id="@+id/group_constraint_layout"
		android:layout_width="0dp"
		android:layout_height="wrap_content"
		android:layout_marginLeft="@dimen/education1_activity_group_constraint_layout_margin_start"
		android:layout_marginTop="64dp"
		android:layout_marginRight="@dimen/education1_activity_group_constraint_layout_margin_end"
		app:layout_constraintLeft_toLeftOf="parent"
		app:layout_constraintRight_toRightOf="parent"
		app:layout_constraintTop_toBottomOf="@id/custom_action_bar">

		<TextView
			android:id="@+id/add_another_text_view"
			android:layout_width="wrap_content"
			android:layout_height="wrap_content"
			android:layout_marginTop="8dp"
			android:layout_marginBottom="36dp"
			android:enabled="false"
			android:fontFamily="@font/font_nunitosans_extrabold"
			android:gravity="left"
			android:lineSpacingMultiplier="1.09"
			android:text="@string/education1_activity_add_another_text_view_text"
			android:textColor="@color/education1_activity_rectangle_constraint_layout_background_color"
			android:textSize="@dimen/education1_activity_add_another_text_view_text_size"
			android:visibility="visible"
			app:layout_constraintBottom_toBottomOf="parent"
			app:layout_constraintLeft_toLeftOf="parent"
			app:layout_constraintTop_toBottomOf="@+id/field_short_placeholder_constraint_layout" />

		<TextView
			android:id="@+id/where_did_you_go_to_text_view"
			android:layout_width="0dp"
			android:layout_height="wrap_content"
			android:fontFamily="@font/font_nunitosans_extrabold"
			android:gravity="left"
			android:lineSpacingMultiplier="1"
			android:text="@string/education1_activity_where_did_you_go_to_text_view_text"
			android:textColor="@color/education1_activity_where_did_you_go_to_text_view_text_color"
			android:textSize="@dimen/education1_activity_where_did_you_go_to_text_view_text_size"
			app:layout_constraintLeft_toLeftOf="parent"
			app:layout_constraintRight_toRightOf="parent"
			app:layout_constraintTop_toTopOf="parent" />

		<androidx.constraintlayout.widget.ConstraintLayout
			android:id="@+id/field_short_placeholder_constraint_layout"
			android:layout_width="@dimen/education1_activity_field_short_placeholder_constraint_layout_width"
			android:layout_height="@dimen/education1_activity_field_short_placeholder_constraint_layout_height"
			android:layout_marginTop="@dimen/education1_activity_field_short_placeholder_constraint_layout_margin_top"
			app:layout_constraintLeft_toLeftOf="parent"
			app:layout_constraintTop_toBottomOf="@+id/where_did_you_go_to_text_view"
			tools:layout_editor_absoluteX="0dp"
			tools:layout_editor_absoluteY="76dp">

			<EditText
				android:id="@+id/school_edit_text"
				android:layout_width="0dp"
				android:layout_height="wrap_content"
				android:layout_marginRight="@dimen/education1_activity_value_edit_text_margin_end"
				android:background="@color/education1_activity_value_edit_text_background_color"
				android:fontFamily="@font/font_nunitosans_regular"
				android:gravity="left"
				android:hint="@string/education1_activity_value_edit_text_hint"
				android:inputType="textCapWords"
				android:lineSpacingMultiplier="1"
				android:textColor="@color/education1_activity_value_edit_text_text_color"
				android:textColorHint="@color/default_hint"
				android:textSize="@dimen/education1_activity_value_edit_text_text_size"
				app:layout_constraintBottom_toBottomOf="parent"
				app:layout_constraintLeft_toLeftOf="parent"
				app:layout_constraintRight_toRightOf="parent"
				app:layout_constraintTop_toTopOf="parent" />

			<androidx.constraintlayout.widget.ConstraintLayout
				android:id="@+id/rectangle_constraint_layout"
				android:layout_width="0dp"
				android:layout_height="@dimen/education1_activity_rectangle_constraint_layout_height"
				android:background="@color/education1_activity_rectangle_constraint_layout_background_color"
				app:layout_constraintBottom_toBottomOf="parent"
				app:layout_constraintLeft_toLeftOf="parent"
				app:layout_constraintRight_toRightOf="parent"
				tools:layout_editor_absoluteX="0dp"
				tools:layout_editor_absoluteY="35dp" />
		</androidx.constraintlayout.widget.ConstraintLayout>

		<androidx.constraintlayout.widget.ConstraintLayout
			android:id="@+id/field_short_placeholder_constraint_layout_2"
			android:layout_width="@dimen/education1_activity_field_short_placeholder_constraint_layout_width"
			android:layout_height="@dimen/education1_activity_field_short_placeholder_constraint_layout_height"
			android:layout_marginTop="@dimen/education1_activity_field_short_placeholder_constraint_layout_margin_top"
			android:visibility="gone"
			app:layout_constraintLeft_toLeftOf="parent"
			app:layout_constraintTop_toBottomOf="@+id/field_short_placeholder_constraint_layout">

			<EditText
				android:id="@+id/school_edit_text_2"
				android:layout_width="0dp"
				android:layout_height="wrap_content"
				android:layout_marginRight="@dimen/education1_activity_value_edit_text_margin_end"
				android:background="@color/education1_activity_value_edit_text_background_color"
				android:fontFamily="@font/font_nunitosans_regular"
				android:gravity="left"
				android:hint="@string/education1_activity_value_edit_text_hint_2"
				android:inputType="textCapWords"
				android:lineSpacingMultiplier="1"
				android:textColor="@color/education1_activity_value_edit_text_text_color"
				android:textColorHint="@color/default_hint"
				android:textSize="@dimen/education1_activity_value_edit_text_text_size"
				app:layout_constraintBottom_toBottomOf="parent"
				app:layout_constraintLeft_toLeftOf="parent"
				app:layout_constraintRight_toRightOf="parent"
				app:layout_constraintTop_toTopOf="parent" />

			<androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="0dp"
				android:layout_height="@dimen/education1_activity_rectangle_constraint_layout_height"
				android:background="@color/education1_activity_rectangle_constraint_layout_background_color"
				app:layout_constraintBottom_toBottomOf="parent"
				app:layout_constraintLeft_toLeftOf="parent"
				app:layout_constraintRight_toRightOf="parent" />
		</androidx.constraintlayout.widget.ConstraintLayout>

	</androidx.constraintlayout.widget.ConstraintLayout>


</androidx.constraintlayout.widget.ConstraintLayout>