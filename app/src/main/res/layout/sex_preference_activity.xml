<androidx.constraintlayout.widget.ConstraintLayout
	xmlns:android="http://schemas.android.com/apk/res/android"
	xmlns:app="http://schemas.android.com/apk/res-auto"
	xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
	android:layout_height="match_parent"
	android:background="@color/sex_preference_activity_sex_preference_constraint_layout_background_color">

    <include
        android:id="@+id/custom_action_bar"
        layout="@layout/custom_action_bar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/group4_constraint_layout"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginTop="54dp"
        android:layout_marginStart="36dp"
        android:layout_marginEnd="36dp"
        app:layout_constraintBottom_toTopOf="@+id/group3_constraint_layout"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/custom_action_bar">

        <TextView
            android:id="@+id/you_are_atext_view"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="54dp"
            android:fontFamily="@font/font_nunitosans_extrabold"
            android:gravity="center"
            android:lineSpacingMultiplier="0.97"
            android:text="@string/sex_preference_activity_you_are_atext_view_text"
            app:layout_constraintStart_toStartOf="@+id/button_preference_man_1"
            android:textColor="@color/sex_preference_activity_you_are_atext_view_text_color"
            android:textSize="@dimen/sex_preference_activity_you_are_atext_view_text_size"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <android.widget.Button
            android:id="@+id/button_preference_man_1"
            style="?android:attr/borderlessButtonStyle"
            android:layout_width="@dimen/sex_preference_activity_radio_button_unselected_button_width"
            android:layout_height="@dimen/sex_preference_activity_radio_button_unselected_button_height"
            android:background="@drawable/sex_preference_activity_radio_button_unselected_button_selector"
            android:fontFamily="@font/font_nunitosans_regular"
            android:gravity="center"
            android:lineSpacingMultiplier="1"
            android:text="@string/sex_preference_activity_radio_button_unselected_button_text"
            android:textAllCaps="false"
            android:textColor="@color/sex_preference_activity_radio_button_unselected_button_text_color"
            android:textSize="@dimen/sex_preference_activity_radio_button_unselected_button_text_size"
            android:layout_marginTop="16dp"
            app:layout_constraintEnd_toStartOf="@+id/button_preference_woman_1"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/you_are_atext_view"/>

        <android.widget.Button
            android:id="@+id/button_preference_woman_1"
            style="?android:attr/borderlessButtonStyle"
            android:layout_width="@dimen/sex_preference_activity_radio_button_unselected_button_width"
            android:layout_height="@dimen/sex_preference_activity_radio_button_unselected_button_height"
            android:background="@drawable/sex_preference_activity_radio_button_unselected_button_selector"
            android:fontFamily="@font/font_nunitosans_regular"
            android:gravity="center"
            android:lineSpacingMultiplier="1"
            android:text="@string/sex_preference_activity_value_text_view_text"
            android:textAllCaps="false"
            android:layout_marginTop="16dp"
            android:textColor="@color/sex_preference_activity_radio_button_unselected_button_text_color"
            android:textSize="@dimen/sex_preference_activity_radio_button_unselected_button_text_size"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintTop_toBottomOf="@+id/you_are_atext_view"
            app:layout_constraintStart_toEndOf="@+id/button_preference_man_1"
            tools:text="@string/sex_preference_activity_radio_button_unselected_copy4_button_text" />

        <TextView
            android:id="@+id/looking_for_text_view"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="36dp"
            android:fontFamily="@font/font_nunitosans_extrabold"
            android:gravity="center"
            android:lineSpacingMultiplier="0.97"
            android:text="@string/sex_preference_looking_for"
            app:layout_constraintStart_toStartOf="@+id/button_preference_man_1"
            android:textColor="@color/sex_preference_activity_you_are_atext_view_text_color"
            android:textSize="@dimen/sex_preference_activity_you_are_atext_view_text_size"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/button_preference_woman_1" />

        <android.widget.Button
            android:id="@+id/button_looking_for_man"
            style="?android:attr/borderlessButtonStyle"
            android:layout_width="@dimen/sex_preference_activity_radio_button_unselected_button_width"
            android:layout_height="@dimen/sex_preference_activity_radio_button_unselected_button_height"
            android:background="@drawable/sex_preference_activity_radio_button_unselected_button_selector"
            android:fontFamily="@font/font_nunitosans_regular"
            android:gravity="center"
            android:lineSpacingMultiplier="1"
            android:text="@string/sex_preference_activity_radio_button_unselected_button_text"
            android:textAllCaps="false"
            android:textColor="@color/sex_preference_activity_radio_button_unselected_button_text_color"
            android:textSize="@dimen/sex_preference_activity_radio_button_unselected_button_text_size"
            android:layout_marginTop="16dp"
            app:layout_constraintEnd_toStartOf="@+id/button_preference_woman_1"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/looking_for_text_view"/>

        <android.widget.Button
            android:id="@+id/button_looking_for_woman"
            style="?android:attr/borderlessButtonStyle"
            android:layout_width="@dimen/sex_preference_activity_radio_button_unselected_button_width"
            android:layout_height="@dimen/sex_preference_activity_radio_button_unselected_button_height"
            android:background="@drawable/sex_preference_activity_radio_button_unselected_button_selector"
            android:fontFamily="@font/font_nunitosans_regular"
            android:gravity="center"
            android:lineSpacingMultiplier="1"
            android:text="@string/sex_preference_activity_value_text_view_text"
            android:textAllCaps="false"
            android:layout_marginTop="16dp"
            android:textColor="@color/sex_preference_activity_radio_button_unselected_button_text_color"
            android:textSize="@dimen/sex_preference_activity_radio_button_unselected_button_text_size"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintTop_toBottomOf="@+id/looking_for_text_view"
            app:layout_constraintStart_toEndOf="@+id/button_preference_man_1"
            tools:text="@string/sex_preference_activity_radio_button_unselected_copy4_button_text" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/group3_constraint_layout"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginBottom="24dp"
        app:layout_constraintBottom_toTopOf="@+id/group5_constraint_layout"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.501"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/group4_constraint_layout">

        <android.widget.Button
            android:id="@+id/button_enable_location"
            style="?android:attr/borderlessButtonStyle"
            android:theme="@style/BottomCTAButton"
            android:layout_width="0dp"
            android:layout_height="@dimen/sex_preference_activity_button_large_active_button_height"
            android:layout_marginStart="@dimen/intro1_activity_button_constraint_layout_margin_start"
            android:layout_marginTop="10dp"
            android:layout_marginEnd="@dimen/intro1_activity_button_constraint_layout_margin_end"
            android:background="@drawable/bottom_button_disabled_state"
            android:enabled="false"
            android:text="@string/sex_preference_activity_button_large_active_button_text"
            android:textColor="@color/sex_preference_activity_button_large_active_button_text_color"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/group5_constraint_layout"
        android:layout_width="@dimen/lottie_imageview_width"
        android:layout_height="@dimen/lottie_imageview_height"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent">

        <com.airbnb.lottie.LottieAnimationView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:lottie_autoPlay="true"
            app:lottie_loop="true"
            app:lottie_rawRes="@raw/clouds" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>