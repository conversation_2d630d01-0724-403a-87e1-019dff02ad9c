<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
	xmlns:app="http://schemas.android.com/apk/res-auto"
	xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
	android:layout_height="match_parent"
	android:background="@color/your_height_activity_your_height_constraint_layout_background_color">

	<include
		android:id="@+id/custom_action_bar"
		layout="@layout/custom_action_bar"
		android:layout_width="match_parent"
		android:layout_height="wrap_content" />

	<androidx.constraintlayout.widget.ConstraintLayout
		android:id="@+id/group5_constraint_layout"
		android:layout_width="0dp"
		android:layout_height="424dp"
		android:layout_marginLeft="18dp"
		android:layout_marginTop="24dp"
		android:layout_marginRight="18dp"
		app:layout_constraintVertical_bias="0"
		app:layout_constraintBottom_toTopOf="@+id/group3_constraint_layout"
		app:layout_constraintStart_toStartOf="parent"
		app:layout_constraintEnd_toEndOf="parent"
		app:layout_constraintTop_toBottomOf="@+id/custom_action_bar">

		<TextView
			android:id="@+id/height_text_view"
			android:layout_width="wrap_content"
			android:layout_height="wrap_content"
			android:layout_marginTop="48dp"
			android:fontFamily="@font/font_nunitosans_extrabold"
			android:gravity="center"
			android:lineSpacingMultiplier="0.85"
			android:text="@string/your_height_activity_text_view_text_view_text"
			android:textColor="@color/your_height_activity_text_view_text_view_text_color"
			android:textSize="@dimen/your_height_activity_text_view_text_view_text_size"
			app:layout_constraintLeft_toLeftOf="parent"
			app:layout_constraintRight_toRightOf="parent"
			app:layout_constraintTop_toBottomOf="@+id/tall_sub_title"
			tools:layout_editor_absoluteX="59dp"
			tools:layout_editor_absoluteY="197dp" />

		<io.apptik.widget.MultiSlider
			app:thumbNumber="1"
			android:id="@+id/slider_single_copy_seek_bar"
			android:layout_width="0dp"
			android:layout_height="wrap_content"
			android:layout_marginLeft="8dp"
			android:layout_marginTop="48dp"
			android:layout_marginRight="8dp"
			android:thumb="@drawable/custom_thumb"
			android:thumbTint="@color/preferences_men_secopy2supernova_activity_group_four_seek_bar_thumb_tint"
			app:rangeColor="@color/preferences_men_secopy2supernova_activity_group_four_seek_bar_progress_tint"
			app:thumbColor="@color/preferences_men_secopy2supernova_activity_group_four_seek_bar_progress_tint"
			app:layout_constraintLeft_toLeftOf="parent"
			app:layout_constraintRight_toRightOf="parent"
			app:layout_constraintTop_toBottomOf="@+id/height_text_view" />

		<TextView
			android:id="@+id/how_tall_are_you_text_view"
			android:layout_width="0dp"
			android:layout_height="wrap_content"
			android:layout_marginTop="@dimen/your_height_activity_how_tall_are_you_text_view_margin_top"
			android:fontFamily="@font/font_nunitosans_extrabold"
			android:gravity="center"
			android:lineSpacingMultiplier="1"
			android:text="@string/your_height_activity_how_tall_are_you_text_view_text"
			android:textColor="@color/your_height_activity_how_tall_are_you_text_view_text_color"
			android:textSize="@dimen/your_height_activity_how_tall_are_you_text_view_text_size"
			app:layout_constraintLeft_toLeftOf="parent"
			app:layout_constraintRight_toRightOf="parent"
			app:layout_constraintTop_toTopOf="parent" />

		<TextView
			android:id="@+id/tall_sub_title"
			android:layout_width="0dp"
			android:layout_height="wrap_content"
			android:layout_marginTop="8dp"
			android:fontFamily="@font/font_nunitosans_regular"
			android:gravity="center"
			android:lineSpacingMultiplier="1"
			android:text="@string/your_height_sub_title"
			android:textColor="@color/grey2"
			android:textSize="16sp"
			app:layout_constraintLeft_toLeftOf="parent"
			app:layout_constraintRight_toRightOf="parent"
			app:layout_constraintTop_toBottomOf="@+id/how_tall_are_you_text_view" />

		<TextView
			android:id="@+id/be_as_accurate_as_po_text_view"
			android:layout_width="0dp"
			android:layout_height="wrap_content"
			android:layout_marginTop="56dp"
			android:layout_marginBottom="36dp"
			android:fontFamily="@font/font_nunitosans_regular"
			android:gravity="center"
			android:lineSpacingMultiplier="1.18"
			android:text="@string/your_height_activity_be_as_accurate_as_po_text_view_text"
			android:textColor="@color/your_height_activity_be_as_accurate_as_po_text_view_text_color"
			android:textSize="@dimen/your_height_activity_be_as_accurate_as_po_text_view_text_size"
			app:layout_constraintBottom_toBottomOf="parent"
			app:layout_constraintEnd_toEndOf="parent"
			app:layout_constraintStart_toStartOf="parent"
			app:layout_constraintTop_toBottomOf="@+id/slider_single_copy_seek_bar" />
	</androidx.constraintlayout.widget.ConstraintLayout>

	<androidx.constraintlayout.widget.ConstraintLayout
		android:id="@+id/group3_constraint_layout"
		android:layout_width="0dp"
		android:layout_height="wrap_content"
		android:layout_marginBottom="24dp"
		app:layout_constraintVertical_bias="1"
		app:layout_constraintBottom_toTopOf="@+id/group6_constraint_layout"
		app:layout_constraintEnd_toEndOf="parent"
		app:layout_constraintStart_toStartOf="parent"
		app:layout_constraintTop_toBottomOf="@+id/group5_constraint_layout">

		<android.widget.Button
			android:id="@+id/button_enable_location"
			style="?android:attr/borderlessButtonStyle"
			android:theme="@style/BottomCTAButton"
			android:layout_width="0dp"
			android:layout_height="@dimen/sex_preference_activity_button_large_active_button_height"
			android:layout_marginStart="@dimen/intro1_activity_button_constraint_layout_margin_start"
			android:layout_marginTop="10dp"
			android:layout_marginEnd="@dimen/intro1_activity_button_constraint_layout_margin_end"
			android:background="@drawable/sex_preference_activity_button_large_active_button_selector"
			android:text="@string/sex_preference_activity_button_large_active_button_text"
			android:textColor="@color/sex_preference_activity_button_large_active_button_text_color"
			app:layout_constraintHorizontal_bias="0.0"
			app:layout_constraintLeft_toLeftOf="parent"
			app:layout_constraintRight_toRightOf="parent"
			app:layout_constraintTop_toTopOf="parent"
			app:layout_constraintVertical_bias="0.0" />

	</androidx.constraintlayout.widget.ConstraintLayout>

	<androidx.constraintlayout.widget.ConstraintLayout
		android:id="@+id/group6_constraint_layout"
		android:layout_width="@dimen/lottie_imageview_width"
		android:layout_height="@dimen/lottie_imageview_height"
		app:layout_constraintBottom_toBottomOf="parent"
		app:layout_constraintLeft_toLeftOf="parent"
		app:layout_constraintRight_toRightOf="parent">

		<com.airbnb.lottie.LottieAnimationView
            android:layout_width="match_parent"
			android:layout_height="match_parent"
			app:layout_constraintBottom_toBottomOf="parent"
			app:layout_constraintEnd_toEndOf="parent"
			app:layout_constraintStart_toStartOf="parent"
			app:layout_constraintTop_toTopOf="parent"
			app:lottie_autoPlay="true"
			app:lottie_loop="true"
			app:lottie_rawRes="@raw/clouds" />
	</androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>