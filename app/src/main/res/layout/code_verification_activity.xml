<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/code_verification_activity_code_verification_constraint_layout_background_color">

    <include
        android:id="@+id/custom_action_bar"
        layout="@layout/custom_action_bar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/group2_constraint_layout"
        android:layout_width="@dimen/code_verification_activity_group2_constraint_layout_width"
        android:layout_height="wrap_content"
        android:layout_marginTop="24dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/custom_action_bar">

        <TextView
            android:id="@+id/i_didn_tget_acode_text_view"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginLeft="48dp"
            android:layout_marginTop="4dp"
            android:layout_marginRight="48dp"
            android:layout_marginBottom="@dimen/code_verification_activity_i_didn_tget_acode_text_view_margin_bottom"
            android:fontFamily="@font/font_nunitosans_regular"
            android:gravity="center"
            android:lineSpacingMultiplier="1.1"
            android:text="@string/code_verification_activity_i_didn_tget_acode_text_view_text"
            android:textColor="@color/code_verification_activity_i_didn_tget_acode_text_view_text_color"
            android:textSize="@dimen/code_verification_activity_i_didn_tget_acode_text_view_text_size"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/group_constraint_layout" />

        <TextView
            android:id="@+id/enter6_digit_code_text_view"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginLeft="12dp"
            android:layout_marginTop="@dimen/code_verification_activity_enter6_digit_code_text_view_margin_top"
            android:layout_marginRight="12dp"
            android:fontFamily="@font/font_nunitosans_extrabold"
            android:gravity="center"
            android:lineSpacingMultiplier="0.97"
            android:text="@string/code_verification_activity_enter6_digit_code_text_view_text"
            android:textColor="@color/code_verification_activity_enter6_digit_code_text_view_text_color"
            android:textSize="@dimen/code_verification_activity_enter6_digit_code_text_view_text_size"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/sent_to_text_view"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/code_verification_activity_sent_to1903495246_text_view_margin_start"
            android:layout_marginTop="@dimen/code_verification_activity_sent_to1903495246_text_view_margin_top"
            android:layout_marginEnd="@dimen/code_verification_activity_sent_to1903495246_text_view_margin_end"
            android:fontFamily="@font/font_nunitosans_regular"
            android:gravity="center"
            android:lineSpacingMultiplier="1.1"
            android:text="@string/code_verification_activity_sent_to_text_view_text"
            android:textColor="@color/code_verification_activity_sent_to1903495246_text_view_text_color"
            android:textSize="@dimen/code_verification_activity_sent_to1903495246_text_view_text_size"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/enter6_digit_code_text_view"
            tools:layout_editor_absoluteX="58dp"
            tools:layout_editor_absoluteY="40dp" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/group_constraint_layout"
            android:layout_width="@dimen/code_verification_activity_group_constraint_layout_width"
            android:layout_height="@dimen/code_verification_activity_group_constraint_layout_height"
            android:layout_marginTop="@dimen/code_verification_activity_group_constraint_layout_margin_top"
            android:layout_marginBottom="20dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/sent_to_text_view">

            <EditText
                android:id="@+id/edit_text_edit_text"
                android:layout_width="225dp"
                android:layout_height="32dp"
                android:background="@color/code_verification_activity_rectangle21_constraint_layout_background_color"
                android:fontFamily="@font/font_nunitosans_regular"
                android:gravity="center"
                android:inputType="number"
                android:letterSpacing="1"
                android:lineSpacingMultiplier="1"
                android:maxLength="6"
                android:selectAllOnFocus="true"
                android:textColor="@color/code_verification_activity_edit_text_edit_text_text_color"
                android:textColorHint="@color/default_hint"
                android:textSize="@dimen/code_verification_activity_edit_text_edit_text_text_size"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />


        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/button_constraint_layout"
        android:layout_width="0dp"
        android:layout_height="@dimen/code_verification_activity_button_constraint_layout_height"
        android:layout_marginStart="@dimen/code_verification_activity_button_constraint_layout_margin_start"
        android:layout_marginTop="52dp"
        android:layout_marginEnd="@dimen/code_verification_activity_button_constraint_layout_margin_end"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/group2_constraint_layout">

        <android.widget.Button
            android:id="@+id/button_enable_location"
            style="?android:attr/borderlessButtonStyle"
            android:theme="@style/BottomCTAButton"
            android:layout_width="0dp"
            android:layout_height="@dimen/code_verification_activity_button_large_active_button_height"
            android:background="@drawable/bottom_button_disabled_state"
            android:enabled="false"
            android:text="@string/code_verification_activity_button_large_active_button_text"
            android:textColor="@color/code_verification_activity_button_large_active_button_text_color"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:layout_editor_absoluteX="0dp"
            tools:layout_editor_absoluteY="0dp" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <ProgressBar
        android:id="@+id/otp_progress_bar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>

</androidx.constraintlayout.widget.ConstraintLayout>