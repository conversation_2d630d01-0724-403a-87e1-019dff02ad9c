<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/top_action_bar_in_live_camera"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingStart="@dimen/top_action_bar_padding_horizontal"
    android:paddingEnd="@dimen/top_action_bar_padding_horizontal"
    android:paddingTop="0dp">

  <ImageView
      android:id="@+id/close_button"
      android:layout_width="wrap_content"
      android:layout_height="wrap_content"
      android:layout_gravity="start"
      android:padding="@dimen/top_action_button_padding"
      app:srcCompat="@drawable/ic_arrow_left"/>

  <TextView
      android:layout_width="wrap_content"
      android:layout_height="wrap_content"
      android:fontFamily="@font/nunitosans_bold"
      android:textColor="@color/black"
      android:layout_centerHorizontal="true"
      android:textSize="18sp"
      android:padding="10dp"
      android:text="Height verification"
      android:gravity="center"/>

  <ImageView
      android:id="@+id/flash_button"
      android:layout_width="wrap_content"
      android:layout_height="wrap_content"
      android:layout_alignParentEnd="true"
      android:padding="@dimen/top_action_button_padding"
      app:srcCompat="@drawable/camera_flash"
      app:tint="@color/grey2" />

</RelativeLayout>
