<androidx.constraintlayout.widget.ConstraintLayout
	xmlns:android="http://schemas.android.com/apk/res/android"
	xmlns:app="http://schemas.android.com/apk/res-auto"
	xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
	android:layout_height="match_parent"
	android:background="@color/hometown_activity_hometown_constraint_layout_background_color">

	<include
		android:id="@+id/custom_action_bar"
		layout="@layout/custom_action_bar"
		android:layout_width="match_parent"
		android:layout_height="wrap_content" />

	<androidx.constraintlayout.widget.ConstraintLayout
		android:id="@+id/button_constraint_layout"
		android:layout_width="0dp"
		android:layout_height="@dimen/hometown_activity_button_constraint_layout_height"
		android:layout_marginLeft="@dimen/hometown_activity_button_constraint_layout_margin_start"
		android:layout_marginRight="@dimen/hometown_activity_button_constraint_layout_margin_end"
		android:layout_marginTop="@dimen/hometown_activity_button_constraint_layout_margin_top"
		app:layout_constraintLeft_toLeftOf="parent"
		app:layout_constraintRight_toRightOf="parent"
		app:layout_constraintTop_toBottomOf="@+id/group_constraint_layout"
		tools:layout_editor_absoluteX="24dp"
		tools:layout_editor_absoluteY="294dp">
	
		<android.widget.Button
			android:id="@+id/button_enable_location"
			style="?android:attr/borderlessButtonStyle"
			android:theme="@style/BottomCTAButton"
			android:layout_width="0dp"
			android:layout_height="@dimen/hometown_activity_button_large_active_button_height"
			android:layout_marginEnd="@dimen/hometown_activity_button_large_active_button_margin_end"
			android:background="@drawable/phone_number_activity_button_large_active_button_selector"
			android:text="@string/hometown_activity_button_large_active_button_text"
			android:textColor="@color/hometown_activity_button_large_active_button_text_color"
			app:layout_constraintBottom_toBottomOf="parent"
			app:layout_constraintLeft_toLeftOf="parent"
			app:layout_constraintRight_toRightOf="parent"
			app:layout_constraintTop_toTopOf="parent"
			tools:layout_editor_absoluteX="0dp"
			tools:layout_editor_absoluteY="0dp"/>
	</androidx.constraintlayout.widget.ConstraintLayout>

	<androidx.constraintlayout.widget.ConstraintLayout
		android:id="@+id/group_constraint_layout"
		android:layout_width="0dp"
		android:layout_height="wrap_content"
		android:layout_marginLeft="@dimen/hometown_activity_group_constraint_layout_margin_start"
		android:layout_marginTop="64dp"
		android:layout_marginRight="@dimen/hometown_activity_group_constraint_layout_margin_end"
		app:layout_constraintLeft_toLeftOf="parent"
		app:layout_constraintRight_toRightOf="parent"
		app:layout_constraintTop_toBottomOf="@id/custom_action_bar">

		<TextView
			android:id="@+id/where_is_your_hometo_text_view"
			android:layout_width="0dp"
			android:layout_height="wrap_content"
			android:fontFamily="@font/font_nunitosans_extrabold"
			android:gravity="left"
			android:lineSpacingMultiplier="1"
			android:text="@string/hometown_activity_where_is_your_hometo_text_view_text"
			android:textColor="@color/hometown_activity_where_is_your_hometo_text_view_text_color"
			android:textSize="@dimen/hometown_activity_where_is_your_hometo_text_view_text_size"
			app:layout_constraintLeft_toLeftOf="parent"
			app:layout_constraintRight_toRightOf="parent"
			app:layout_constraintTop_toTopOf="parent" />

		<androidx.constraintlayout.widget.ConstraintLayout
			android:id="@+id/field_short_placeholder_constraint_layout"
			android:layout_width="@dimen/hometown_activity_field_short_placeholder_constraint_layout_width"
			android:layout_height="@dimen/hometown_activity_field_short_placeholder_constraint_layout_height"
			android:layout_marginTop="10dp"
			app:layout_constraintBottom_toBottomOf="parent"
			app:layout_constraintLeft_toLeftOf="parent"
			app:layout_constraintTop_toBottomOf="@+id/where_is_your_hometo_text_view">

			<EditText
				android:id="@+id/home_town_edit_text"
				android:layout_width="0dp"
				android:layout_height="wrap_content"
				android:background="@color/hometown_activity_value_edit_text_background_color"
				android:fontFamily="@font/font_nunitosans_regular"
				android:gravity="left"
				android:hint="@string/hometown_activity_value_edit_text_hint"
				android:inputType="textCapWords"
				android:lineSpacingMultiplier="1"
				android:textColor="@color/hometown_activity_value_edit_text_text_color"
				android:textColorHint="@color/default_hint"
				android:textSize="@dimen/hometown_activity_value_edit_text_text_size"
				app:layout_constraintBottom_toBottomOf="parent"
				app:layout_constraintLeft_toLeftOf="parent"
				app:layout_constraintRight_toRightOf="parent"
				app:layout_constraintTop_toTopOf="parent" />

			<androidx.constraintlayout.widget.ConstraintLayout
				android:id="@+id/rectangle_constraint_layout"
				android:layout_width="0dp"
				android:layout_height="@dimen/hometown_activity_rectangle_constraint_layout_height"
				android:background="@color/hometown_activity_rectangle_constraint_layout_background_color"
				app:layout_constraintBottom_toBottomOf="parent"
				app:layout_constraintLeft_toLeftOf="parent"
				app:layout_constraintRight_toRightOf="parent"
				tools:layout_editor_absoluteX="0dp"
				tools:layout_editor_absoluteY="35dp" />
		</androidx.constraintlayout.widget.ConstraintLayout>
	</androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>