<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/profession_activity_toolbar_background_color"
    android:paddingTop="0dp">

    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="#FFF"
        android:paddingStart="16dp"
        android:paddingEnd="16dp">
        <TextView
            android:id="@+id/skip_text_view"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/profession_activity_edit_menu_item_text"
            android:fontFamily="@font/font_nunitosans_regular"
            android:textSize="16sp"
            android:padding="10dp"
            android:textColor="#646C70"
            android:layout_gravity="end"
            android:layout_marginEnd="12dp"/>
    </androidx.appcompat.widget.Toolbar>

</RelativeLayout>