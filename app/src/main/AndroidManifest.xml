<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    tools:ignore="LockedOrientationActivity">

    <uses-feature
        android:name="android.hardware.camera"
        android:required="false" />

    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE"
        android:maxSdkVersion="32" />
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />

    <queries>
        <intent>
            <action android:name="android.media.action.IMAGE_CAPTURE" />
        </intent>
        <intent>
            <action android:name="android.intent.action.VIEW" />
        </intent>
    </queries>

    <application
        android:name=".DateUpApplication"
        android:allowBackup="false"
        android:fullBackupContent="false"
        android:icon="@drawable/ic_launcher"
        android:label="@string/app_name"
        android:supportsRtl="true"
        android:theme="@style/AppTheme">
        <activity
            android:name=".activity.genderFlow.WhoShouldSeeActivity"
            android:exported="false"
            android:label=""
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar"/>
        <activity
            android:name=".activity.genderFlow.GenderListActivity"
            android:exported="false"
            android:label=""
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar"/>
        <activity
            android:name=".activity.genderFlow.InterestedInActivity"
            android:exported="false"
            android:label=""
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar"/>
        <activity
            android:name=".activity.genderFlow.IdentifyYourselfActivity"
            android:exported="false"
            android:label=""
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar"/>
        <activity
            android:name=".activity.TestActivity"
            android:exported="false" />
        <activity
            android:name=".activity.DateUpPledgeActivity"
            android:exported="false"
            android:label=""
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar" />
        <activity
            android:name=".subscriptions.views.DateUpPlusCelebrationActivity"
            android:label=""
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar" />
        <activity
            android:name=".autoComplete.AddNewCityActivity"
            android:label=""
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar"
            android:windowSoftInputMode="stateVisible" />
        <activity
            android:name=".activity.HowHeightVerificationWorks"
            android:label=""
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar" />
        <activity
            android:name=".activity.SplashActivity"
            android:exported="true"
            android:label="@string/app_name"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar">
            <intent-filter>
                <category android:name="android.intent.category.LAUNCHER" />

                <action android:name="android.intent.action.MAIN" />
            </intent-filter>
            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="dateupapis.co"
                    android:pathPrefix="/link"
                    android:scheme="https" />
            </intent-filter>
            <!-- Branch URI Scheme -->
            <intent-filter>
                <data
                    android:host="open"
                    android:scheme="dateup" />

                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
            </intent-filter>
            <!-- Branch App Links -->
            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="dateup.app.link"
                    android:scheme="https" />
                <data
                    android:host="dateup-alternate.app.link"
                    android:scheme="https" />
            </intent-filter>
            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="dateup.test-app.link"
                    android:scheme="https" />
                <data
                    android:host="dateup-alternate.test-app.link"
                    android:scheme="https" />
            </intent-filter>
        </activity>
        <activity
            android:name=".heightVerification.ui.HeightVerificationScanResult"
            android:label=""
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar" />
        <activity
            android:name=".heightVerification.ui.HeightVerificationScan"
            android:label=""
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar" />
        <activity
            android:name=".subscriptions.views.SubscriptionActivity"
            android:label=""
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar" />
        <activity
            android:name=".ui.settings.SettingsActivity"
            android:label=""
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar" />
        <activity
            android:name=".paidVersion.likesYou.LikesYouActivity"
            android:label=""
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar" />
        <activity
            android:name=".paidVersion.likesYou.UpgradeActivity"
            android:exported="true"
            android:label=""
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar">
            <intent-filter>
                <action android:name="OPEN_UPGRADE_ACTIVITY" />

                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>
        <activity
            android:name=".ui.chat.ChatActivity"
            android:exported="true"
            android:label=""
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar">
            <intent-filter>
                <action android:name="OPEN_CHAT_ACTIVITY" />

                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>
        <activity
            android:name=".ui.browseProfiles.BrowseProfilesActivity"
            android:label=""
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar" />
        <activity
            android:name=".ui.BottomNavigationBarActivity"
            android:label=""
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar" />
        <activity
            android:name=".activity.BetaGroupActivity"
            android:label=""
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar" />
        <activity
            android:name=".activity.OtherUserProfileModal"
            android:label=""
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar" />
        <activity
            android:name=".activity.MutualMatchActivity"
            android:label=""
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar" />
        <activity
            android:name=".activity.UpdatePhoneNumberOtpActivity"
            android:label=""
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar"
            android:windowSoftInputMode="stateVisible" />
        <activity
            android:name=".activity.UpdatePhoneNumberActivity"
            android:label=""
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar"
            android:windowSoftInputMode="stateVisible" />
        <activity
            android:name=".activity.UpdateEmailActivity"
            android:label=""
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar"
            android:windowSoftInputMode="stateVisible" />
        <activity
            android:name=".chat.message.MessageListActivity"
            android:label=""
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".activity.SettingsIceBreakerListActivity"
            android:label=""
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar" />
        <activity
            android:name=".activity.SettingsIceBreakerTitleActivity"
            android:label=""
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar"
            android:windowSoftInputMode="stateVisible" />
        <activity
            android:name=".activity.EditProfileModalActivity"
            android:label=""
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar" />
        <activity
            android:name=".activity.WelcomeActivity"
            android:label=""
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar" />
        <activity
            android:name=".activity.GuestMatchMakingActivity"
            android:label=""
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar" />
        <activity
            android:name=".activity.HeightCelebrationGuestsActivity"
            android:label=""
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar" />
        <activity
            android:name=".activity.HeightCelebrationMembersActivity"
            android:label=""
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar" />
        <activity
            android:name=".activity.AgerestrictionActivity"
            android:label=""
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar" />
        <activity
            android:name=".activity.GuestHeightPreferenceActivity"
            android:label=""
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar" />
        <activity
            android:name=".activity.Intro1Activity"
            android:label="@string/app_name"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar" />
        <activity
            android:name=".activity.PhoneNumberActivity"
            android:label=""
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar"
            android:windowSoftInputMode="stateAlwaysVisible" />
        <activity
            android:name=".activity.CodeVerificationActivity"
            android:label=""
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar"
            android:windowSoftInputMode="stateAlwaysVisible" />
        <activity
            android:name=".activity.EmailActivity"
            android:label=""
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar"
            android:windowSoftInputMode="stateVisible" />
        <activity
            android:name=".activity.EnableLocationActivity"
            android:label=""
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar" />
        <activity
            android:name=".activity.SpecifyNeighborhoodActivity"
            android:label=""
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar" />
        <activity
            android:name=".activity.FirstNameActivity"
            android:label=""
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar"
            android:windowSoftInputMode="stateVisible" />
        <activity
            android:name=".activity.DateOfBirthActivity"
            android:label=""
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar"
            android:windowSoftInputMode="stateVisible" />
        <activity
            android:name=".activity.SexPreferenceActivity"
            android:label=""
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar" />
        <activity
            android:name=".activity.YourHeightActivity"
            android:label=""
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar" />
        <activity
            android:name=".activity.Images3Activity"
            android:label=""
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar" />
        <activity
            android:name=".activity.ProfessionActivity"
            android:label=""
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar"
            android:windowSoftInputMode="stateVisible" />
        <activity
            android:name=".activity.Education1Activity"
            android:label=""
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar"
            android:windowSoftInputMode="stateVisible" />
        <activity
            android:name=".activity.HometownActivity"
            android:label=""
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar"
            android:windowSoftInputMode="stateVisible" />
        <activity
            android:name=".activity.Icebreaker1Activity"
            android:label=""
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar" />
        <activity
            android:name=".activity.Icebreaker2Activity"
            android:label=""
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar"
            android:windowSoftInputMode="stateVisible" />
        <activity
            android:name=".activity.Icebreaker3Activity"
            android:label=""
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar" />
        <activity
            android:name=".activity.Icebreaker4Activity"
            android:label=""
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar"
            android:windowSoftInputMode="stateVisible" />
        <activity
            android:name=".activity.BlankNoNetworkConnectionActivity"
            android:label=""
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar" />
        <activity
            android:name=".activity.LocationLockedActivity"
            android:label=""
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar" />
        <activity
            android:name=".heightVerification.ui.LiveBarcodeScanningActivity"
            android:label=""
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar" />
        <activity
            android:name="com.yalantis.ucrop.UCropActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar" />

        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="com.dateup.provider"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_paths" />
        </provider>

        <service
            android:name=".fcm.DateUpFirebaseMessagingService"
            android:exported="false">
            <intent-filter>
                <action android:name="com.google.firebase.MESSAGING_EVENT" />
            </intent-filter>
        </service>
        <service
            android:name=".services.BackgroundLocationIntentService"
            android:exported="false"
            android:permission="android.permission.BIND_JOB_SERVICE" />

        <meta-data
            android:name="com.google.firebase.messaging.default_notification_icon"
            android:resource="@drawable/icon_notification" />
        <meta-data
            android:name="com.google.firebase.messaging.default_notification_color"
            android:resource="@color/notifications_settings_switch_active" /> <!-- Branch init -->
        <meta-data
            android:name="io.branch.sdk.BranchKey"
            android:value="key_live_em7Px1DXY6RoMQc4U36euafiDEfJIKHO" />
        <meta-data
            android:name="io.branch.sdk.BranchKey.test"
            android:value="key_test_ehZLs6wXZ5LhKVi0WmpYTfmiwvgGVHKX" />
        <meta-data
            android:name="io.branch.sdk.TestMode"
            android:value="false" />
        <meta-data
            android:name="com.facebook.sdk.ApplicationId"
            android:value="@string/facebook_app_id" />
        <meta-data
            android:name="com.facebook.sdk.ClientToken"
            android:value="@string/facebook_client_token" />
    </application>

</manifest>