package com.dateup.android.services

import android.content.Context
import com.dateup.android.AccountPreferences
import com.dateup.android.models.LocationLocationResponse
import com.dateup.android.models.UserObject
import com.dateup.android.utils.Constants
import com.google.firebase.functions.FirebaseFunctions
import com.google.gson.Gson
import timber.log.Timber

class LockedLocationUtil(val context: Context) {

    fun isUserLocationUnlocked(userLat: Double, userLong: Double, city: String, firebaseUID: String, callback: (<PERSON><PERSON><PERSON>, LocationLocationResponse?) -> Unit) {

        try {
            val functions = FirebaseFunctions.getInstance()
            val data = hashMapOf(
                    "lat" to userLat,
                    "long" to userLong,
                    "city" to city,
                    "uid" to firebaseUID
            )
            functions
                    .getHttpsCallable("checkLocationStatus")
                    .call(data)
                    .addOnCompleteListener { task ->
                        if (task.isSuccessful) {
                            val result: LocationLocationResponse? = Gson().fromJson(task.result?.getData()?.toString(), LocationLocationResponse::class.java)
                            if (result?.isOnWaitlist == false && result.holdNewUsers == false) {
                                callback(true, result)
                                UserObject.isUserLocationUnlocked = true
                                AccountPreferences.getInstance(context).setValue(Constants.isUserLocationUnlocked, true)
                            }else {
                                callback(false, result)
                                UserObject.isUserLocationUnlocked = false
                            }
                        } else {
                            callback(false, null)
                            UserObject.isUserLocationUnlocked = false
                        }
                    }
        }catch (exception: Exception) {
            Timber.e("Error in fetching location locked data: $exception")
            callback(false, null)
        }
    }
}

