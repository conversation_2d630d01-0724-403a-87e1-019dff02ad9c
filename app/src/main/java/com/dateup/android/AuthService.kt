package com.dateup.android

import android.content.Context
import com.dateup.android.deepLinking.PromotionsHandler
import com.dateup.android.glide.MyAppGlideModule
import com.dateup.android.models.UserObject
import com.dateup.android.utils.Constants
import com.google.firebase.auth.FirebaseAuth

class AuthService {

    companion object {

        fun cleanup(context: Context?) {

            val influencerId = AccountPreferences.getInstance(context).getStringValue(Constants.influencerId, "")
            val influencerName = AccountPreferences.getInstance(context).getStringValue(Constants.influencerName, "")
            val installSource = AccountPreferences.getInstance(context).getStringValue(com.dateup.android.utils.Constants.installSource, "")

            AccountPreferences.getInstance(context).clear()
            UserObject.cleanup()

            if (!influencerId.isNullOrEmpty() && context != null) {
                PromotionsHandler.handleInfluencerSignups(context, influencerId, influencerName)
            }

            if (!installSource.isNullOrEmpty() && context != null) {
                PromotionsHandler.handleBranchCampaignSignups(context, installSource)
            }

            context?.let {
                MyAppGlideModule.clearCache(it)
            }
        }

        fun isUserAuthenticated(): Boolean {
            return FirebaseAuth.getInstance().currentUser != null
        }
    }
}