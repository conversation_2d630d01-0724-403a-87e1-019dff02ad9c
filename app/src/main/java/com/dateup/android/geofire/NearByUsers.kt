package com.dateup.android.geofire

import com.dateup.android.models.GetNearByUsersResult
import com.dateup.android.models.NearByUser
import com.google.firebase.functions.FirebaseFunctions
import com.google.firebase.perf.FirebasePerformance
import com.google.gson.Gson
import timber.log.Timber

object NearByUsers {

    fun getNearByUsers(uid: String, latitude:Double, longitude:Double, radius: Int, callback: (List<NearByUser>?, GetUsersResponse) -> Unit) {
        Timber.d("getNearByUsers API called")
        val trace = FirebasePerformance.getInstance().newTrace("getNearByUsers_Trace")
        trace.start()
        try {
            val functions = FirebaseFunctions.getInstance()
            val data = hashMapOf(
                "uid" to uid,
                "latitude" to latitude,
                "longitude" to longitude,
                "radius" to radius
            )
            functions
                .getHttpsCallable("getUsers")
                .call(data)
                .addOnCompleteListener { task ->
                    if (task.isSuccessful) {
                        val result = Gson().fromJson(task.result?.getData()?.toString(), GetNearByUsersResult::class.java)
                        callback(result.users, GetUsersResponse.SUCCESS)
                    }else {
                        callback(null, GetUsersResponse.FAILED)
                        Timber.e("getNearByUsers API failed: ${task.exception}")
                    }
                }
        }catch (exception: Exception) {
            callback(null, GetUsersResponse.FAILED)
            Timber.e("Exception in getNearByUsers API: $exception")
        }
        trace.stop()
    }
}

enum class GetUsersResponse{
    SUCCESS,
    FAILED
}