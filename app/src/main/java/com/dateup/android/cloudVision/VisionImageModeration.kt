package com.dateup.android.cloudVision

import android.util.Base64.DEFAULT
import android.util.Base64.encodeToString
import com.dateup.android.BuildConfig
import com.dateup.android.models.ImageModerationResponse
import com.google.firebase.functions.FirebaseFunctions
import com.google.firebase.perf.FirebasePerformance
import com.google.gson.Gson
import timber.log.Timber

object VisionImageModeration {
    fun checkImageForModerationNode(uid: String, imageByteArray: ByteArray, callback:(Boolean) -> Unit) {
        val trace = FirebasePerformance.getInstance().newTrace("CheckImageForModerationNode_Trace")
        trace.start()
        try {
            if (BuildConfig.DEBUG) {
                callback(false)
                return
            }
            val functions = FirebaseFunctions.getInstance()
            val data = hashMapOf(
                    "uid" to uid,
                    "encodedImageString" to encodeToString(imageByteArray, DEFAULT)
            )
            functions
                    .getHttpsCallable("scanImageForModeration")
                    .call(data)
                    .addOnCompleteListener { task ->
                       if (task.isSuccessful) {
                           val result = Gson().fromJson(task.result?.getData()?.toString(), ImageModerationResponse::class.java)
                           callback(result?.isPhotoViolated?: false)
                       }else {
                           callback(false)
                       }
                    }
        }catch (exception: Exception) {
            Timber.e("Error in image moderation: $exception")
            callback(false)
        }
        trace.stop()
    }
}
