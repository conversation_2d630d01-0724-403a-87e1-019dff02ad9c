package com.dateup.android.ui.browseProfiles

import android.animation.Animator
import android.annotation.SuppressLint
import android.app.Activity
import android.content.*
import android.graphics.drawable.ColorDrawable
import android.net.Uri
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.provider.Settings
import android.text.TextUtils
import android.view.View
import android.widget.*
import androidx.appcompat.widget.Toolbar
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.content.ContextCompat
import androidx.core.content.res.ResourcesCompat
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import co.ceryle.segmentedbutton.SegmentedButton
import co.ceryle.segmentedbutton.SegmentedButtonGroup
import com.airbnb.lottie.LottieAnimationView
import com.dateup.android.*
import com.dateup.android.activity.Icebreaker1Activity
import com.dateup.android.activity.MutualMatchActivity
import com.dateup.android.analytics.AnalyticsTrackingService
import com.dateup.android.analytics.DISLIKE_USER
import com.dateup.android.analytics.LIKE_USER
import com.dateup.android.analytics.MUTUAL_MATCH
import com.dateup.android.customViews.AppReview
import com.dateup.android.customViews.ToolTipView
import com.dateup.android.deepLinking.AppInvites
import com.dateup.android.extensions.getDoubleValue
import com.dateup.android.extensions.getIntValue
import com.dateup.android.extensions.launchModeWithSingleTop
import com.dateup.android.firebase.*
import com.dateup.android.fragment.SendMessageDialogFragment
import com.dateup.android.geofire.GetUsersResponse
import com.dateup.android.geofire.NearByUsers
import com.dateup.android.glide.ImageLoaderModule
import com.dateup.android.heightVerification.ui.HeightVerificationScan
import com.dateup.android.models.*
import com.dateup.android.paidVersion.NumLikesRestriction
import com.dateup.android.paidVersion.NumLikesRestriction.deviceDateBasedOnServer
import com.dateup.android.paidVersion.NumLikesRestriction.deviceLikeCounter
import com.dateup.android.paidVersion.NumLikesRestriction.likesThreshold
import com.dateup.android.paidVersion.NumLikesRestriction.tempLikeCounterInMemory
import com.dateup.android.paidVersion.changeLocation.UnlockedLocationsApi
import com.dateup.android.paidVersion.changeLocation.UnlockedLocationsFragment
import com.dateup.android.paidVersion.changeLocation.UnlockedLocationsFragment.Companion.TELEPORT_FROM_BROWSE_PROFILES
import com.dateup.android.paidVersion.changeLocation.UnlockedLocationsFragment.Companion.TELEPORT_ORIGIN
import com.dateup.android.services.BackgroundLocationIntentService
import com.dateup.android.services.LockedLocationUtil
import com.dateup.android.subscriptions.viewModels.BillingViewModel
import com.dateup.android.subscriptions.views.SubscriptionActivity
import com.dateup.android.ui.BottomNavigationBarActivity
import com.dateup.android.ui.settings.SettingsActivity
import com.dateup.android.utils.*
import com.dateup.android.utils.Constants.conversations
import com.dateup.android.utils.Constants.isAMember
import com.dateup.android.viewModels.SpacesImagesViewModel
import com.google.firebase.database.FirebaseDatabase
import timber.log.Timber
import java.text.SimpleDateFormat
import java.util.*
import kotlin.collections.ArrayList
import kotlin.collections.LinkedHashMap

class BrowseProfilesActivity : BottomNavigationBarActivity() {

    private lateinit var mLayoutContainer: FrameLayout

    private lateinit var educationTextViewTitle: TextView
    private lateinit var professionTextView: TextView
    private lateinit var ageTextView: TextView
    private lateinit var distanceTextView: TextView
    private lateinit var heightTextView: TextView
    private lateinit var educationTextView: TextView
    private lateinit var hometownTextView: TextView
    private lateinit var hometownTextViewHeader: TextView
    private lateinit var iceBreakerQuestion1TextView: TextView
    private lateinit var iceBreakerAnswer1TextView: TextView
    private lateinit var iceBreakerQuestion2TextView: TextView
    private lateinit var iceBreakerAnswer2TextView: TextView
    private lateinit var userNameTextView: TextView
    private lateinit var buttonGhostButton: Button
    private lateinit var firstPhotoImageView: ImageView
    private lateinit var secondPhotoImageView: ImageView
    private lateinit var thirdPhotoImageView: ImageView
    private lateinit var firstIcebreakerBackgroundImageView: ImageView
    private lateinit var secondIcebreakerBackgroundImageView: ImageView
    private lateinit var fourthPhotoImageView: ImageView
    private lateinit var fifthPhotoImageView: ImageView
    private lateinit var sixthPhotoImageView: ImageView

    private lateinit var progressBar: ProgressBar
    private lateinit var topHeelsIcon: ImageView
    private lateinit var bottomHeelsIcon: ImageView
    private lateinit var bottomHeelsTextViewPreview: TextView
    private lateinit var bottomHeelsSectionLayout: ConstraintLayout
    private lateinit var progressTextView: TextView
    private lateinit var locationIconImageView: ImageView
    private lateinit var ageHeaderTextView: TextView
    private lateinit var professionHeaderTextView: TextView
    private lateinit var userLocationTextView: TextView

    private lateinit var iceBreaker1Layout: ConstraintLayout
    private lateinit var iceBreaker2Layout: ConstraintLayout

    private lateinit var scrollBar: ScrollView

    private lateinit var likeButton: Button
    private lateinit var disLikeButton: Button

    private lateinit var sendDMButton: Button

    private lateinit var lottieAnimationView: LottieAnimationView

    //toolbar
    private lateinit var memberButton: SegmentedButton
    private lateinit var guestButton: SegmentedButton
    private lateinit var segmentedButtonGroup: SegmentedButtonGroup
    private lateinit var membersBrowseTextView: TextView
    private lateinit var toolbar: Toolbar

    private lateinit var mContext: Context

    private var mainUserId = ""
    private var previousMatchUserId = ""

    private var isMembersTabSelected = true
    private var isGuestsTabSelected = false
    private var shouldShowGuests: Boolean = false

    private var mainUserInfo: User? = null

    private lateinit var mainUserGender: String

    var usersMembersMap: LinkedHashMap<String?, NearByUser?> = LinkedHashMap()
    var usersMembersKeysArrayList: ArrayList<String?> = ArrayList()
    private var usersMembersListCounter = 0

    var usersGuestsMap: LinkedHashMap<String?, NearByUser?> = LinkedHashMap()
    var usersGuestsKeysArrayList: ArrayList<String?> = ArrayList()
    private var usersGuestsListCounter = 0

    private var firebaseDatabaseUtil: FirebaseDatabaseUtil? = null

    private val enableLocationInSettingsRequestCode = 2000

    private var userCurrentCity = ""

    var firstPhotoRef = ""
    var secondPhotoRef = ""
    var thirdPhotoRef = ""
    var fourthPhotoRef = ""
    var fifthPhotoRef = ""
    var sixthPhotoRef = ""

    private lateinit var spacesImagesViewModel: SpacesImagesViewModel

    private var isLoadingProfiles = false

    private lateinit var appLifecycleObserver: ForegroundBackgroundListener
    var appWentToBackground = false
    var reviewShown = false
    var freeTrailShown = false

    var shouldFetchUsers = true

    private lateinit var settingsTopImageView: ImageView

    private lateinit var activityBrowseProfilesConstraintLayout: ConstraintLayout

    private lateinit var browseWithCityIcon: TextView
    private lateinit var browseWithCityTextView: TextView

    private lateinit var layoutEnableLocation: View
    private lateinit var layoutOutOfPeople: View
    private lateinit var userErrorLayout: View
    private lateinit var getUsersApiErrorLayout: View

    private lateinit var heightVerifiedBadge: ImageView
    private lateinit var heightVerifiedTextView: TextView

    private lateinit var likeDislikeFrameLayout: FrameLayout
    private lateinit var guestHiddenFragment: View

    companion object {

        fun newIntent(context: Context): Intent {

            return Intent(context, BrowseProfilesActivity::class.java)
        }
    }

    override fun getBottomNavigationBarItem(): Int {

        return R.id.navigation_home
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        spacesImagesViewModel = ViewModelProvider(this).get(SpacesImagesViewModel::class.java)
        checkForSubscriptionsStatusAndUpdateDB()

        init()

        if (!UserObject.isLocationSentToServer) {
            setLocation()
        }

        appLifecycleObserver = ForegroundBackgroundListener(
            onAppGoesToBackground = {
                appWentToBackground = true
            },
            onAppEntersForeground = {
                if (isBrowseProfilesScreenActive &&
                    appWentToBackground && !isLoadingProfiles
                    && usersGuestsMap.isEmpty() && usersMembersMap.isEmpty()) {
                    Timber.d("Fetching users again after app brought up from background")
                    readMainUserInfo()
                }
                appWentToBackground = false
            })

        appLifecycleObserver.attach()

        val tutorialShown = AccountPreferences.getInstance(this).getBooleanValue(Constants.tutorialShown, false)
        if (!tutorialShown) {
            AccountPreferences.getInstance(this).setValue(Constants.tutorialShown, true)

            Handler(Looper.getMainLooper()).postDelayed({
                ToolTipView().showTour(this)
            }, 1200)
        }

        reviewShown = AccountPreferences.getInstance(this).getBooleanValue(Constants.reviewShown, false)
        freeTrailShown = AccountPreferences.getInstance(this).getBooleanValue(Constants.freeTrailShown, false)
    }

    private fun init() {

        Timber.d("Browse profiles activity init")

        mContext = this

        firebaseDatabaseUtil = FirebaseDatabaseUtil(mContext)

        mLayoutContainer = findViewById(R.id.layout_container)

        val browseProfilesLayout = View.inflate(this, R.layout.activity_browse_profiles, null)
        mLayoutContainer.addView(browseProfilesLayout)

        setupToolBar()

        settingsTopImageView = findViewById(R.id.settings_top_image_view)

        settingsTopImageView.setOnClickListener {
            startPreferencesFragment()
        }

        scrollBar = findViewById(R.id.view_scroll_view)

        // Configure Product Designer component
        professionTextView = findViewById(R.id.user_profession_text_view)

        educationTextViewTitle = findViewById(R.id.education_text_view)

        // Configure Age component
        ageTextView = findViewById(R.id.user_age_text_view)

        // Configure Education component
        educationTextView = findViewById(R.id.user_education_text_view)

        locationIconImageView = findViewById(R.id.location_image_view)

        // Configure Hometown component
        hometownTextView = findViewById(R.id.user_home_town_text_view)

        hometownTextViewHeader = findViewById(R.id.user_home_town_text_view)

        // Configure How do you describe component
        iceBreakerQuestion1TextView = findViewById(R.id.ice_breaker_1_question_text_view)

        // Configure I’m definitely fun a component
        iceBreakerAnswer1TextView = findViewById(R.id.ice_breaker_1_answer_text_view)

        // Configure How do you describe component
        iceBreakerQuestion2TextView = findViewById(R.id.ice_breaker_2_question_text_view)

        // Configure I’m definitely fun a component
        iceBreakerAnswer2TextView = findViewById(R.id.ice_breaker_2_answer_text_view)


        // Configure Jon component
        userNameTextView = findViewById(R.id.user_name_text_view)

        ageHeaderTextView = findViewById(R.id.age_text_view)
        professionHeaderTextView = findViewById(R.id.profession_text_view)

        likeButton = findViewById(R.id.like_button)

        disLikeButton = findViewById(R.id.dislike_button)

        sendDMButton = findViewById(R.id.dm_button)

        layoutEnableLocation = findViewById(R.id.layout_enable_location)
        layoutOutOfPeople = findViewById(R.id.layout_out_of_people)
        userErrorLayout = findViewById(R.id.user_error)
        getUsersApiErrorLayout = findViewById(R.id.get_users_api_error_container)
        browseWithCityIcon = findViewById(R.id.browse_city_with_icon)
        browseWithCityTextView = findViewById(R.id.browse_city_text)

        heightVerifiedBadge = findViewById(R.id.height_verified_badge)
        heightVerifiedTextView = findViewById(R.id.height_verified_text_view)

        likeDislikeFrameLayout = findViewById(R.id.like_dislike_frame)

        guestHiddenFragment = findViewById(R.id.guests_hidden_fragment)

        heightTextView = findViewById(R.id.user_height_text_view)

        firstPhotoImageView = findViewById(R.id.user_first_photo_image_view)

        secondPhotoImageView = findViewById(R.id.user_second_photo_image_view)

        thirdPhotoImageView = findViewById(R.id.user_third_photo_image_view)

        fourthPhotoImageView = findViewById(R.id.user_fourth_photo_image_view)

        fifthPhotoImageView = findViewById(R.id.user_fifth_photo_image_view)

        sixthPhotoImageView = findViewById(R.id.user_sixth_photo_image_view)

        iceBreaker1Layout = findViewById(R.id.profilecard_blank_photo_copy_constraint_layout)

        iceBreaker2Layout = findViewById(R.id.profilecard_blank_photo_copy3_constraint_layout)

        firstIcebreakerBackgroundImageView = findViewById(R.id.rectangle_image_view)
        secondIcebreakerBackgroundImageView = findViewById(R.id.rectangle_two_image_view)

        activityBrowseProfilesConstraintLayout = findViewById(R.id.activity_browse_profiles)

        setImageViewHeightBasedOnDeviceWidth()

        distanceTextView = findViewById(R.id.distance_text_view)

        userLocationTextView = findViewById(R.id.user_location_text_view)

        topHeelsIcon = findViewById(R.id.top_heels_icon)
        bottomHeelsIcon = findViewById(R.id.boots_bottom_image_view)
        bottomHeelsTextViewPreview = findViewById(R.id.user_match_boot_preview_text_view)
        bottomHeelsSectionLayout = findViewById(R.id.group10_constraint_layout)

        progressBar = findViewById(R.id.progressBar)
        progressTextView = findViewById(R.id.progressTextView)

        showLoading()

        lottieAnimationView = findViewById(R.id.user_like_dislike_animation_view)

        shouldShowGuests = AccountPreferences.getInstance(this).getBooleanValue(Constants.showGuests, false)
        mainUserGender = AccountPreferences.getInstance(this).getStringValue(Constants.gender, "")
        mainUserId = AccountPreferences.getInstance(this).getStringValue(Constants.firebaseUserId, "")
        val isUserMember = AccountPreferences.getInstance(this).getBooleanValue(isAMember, false)

        // Configure button/ghost component
        buttonGhostButton = findViewById(R.id.report_user_button)
        buttonGhostButton.setOnClickListener { view ->
            this.onButtonGhostPressed()
        }

        likeButton.setOnClickListener { view ->

            val iceBreakerQuestion1 = AccountPreferences.getInstance(this).getStringValue(Constants.iceBreaker1, "")
            val iceBreakerQuestion2 = AccountPreferences.getInstance(this).getStringValue(Constants.iceBreaker2, "")
            if (iceBreakerQuestion1.isNullOrEmpty() && iceBreakerQuestion2.isNullOrEmpty()) {

                AlertDialogView.showAlertDialog(context = this,
                        title = getString(R.string.complete_profile),
                        message = getString(R.string.complete_profile_desc),
                        buttonPositiveText = getString(R.string.continue_text),
                        buttonNegativeText = getString(R.string.cancel)) { dialog, which ->

                    if (which == DialogInterface.BUTTON_POSITIVE) {

                        startActivity(Icebreaker1Activity.newIntent(this))
                    } else {

                        dialog.cancel()
                    }
                }
            } else {

                onLikeButtonClicked()
            }
        }

        disLikeButton.setOnClickListener { view ->
            onDisLikeButtonClicked()
        }

        hideProfession()
        hideHomeTown()
        hideSchool()

        if (isUserMember) {
            showToolbar()
        } else {
            hideToolbar()
        }

        if (deviceDateBasedOnServer != null) {
            readMainUserInfo()
        }else {
             NumLikesRestriction.setDeviceLikeCounterAndDate(this) {
                readMainUserInfo()
            }
        }

        UserObject.userPreferencesChangedLiveData.observe(this) { changed ->
            Timber.d("User preferences changed")
            if (changed && !isLoadingProfiles) {
                UserObject.userPreferencesUpdatedAPICalled = false
                shouldShowGuests = AccountPreferences.getInstance(this).getBooleanValue(Constants.showGuests, false)
                if (isGuestsTabSelected && shouldShowGuests) {
                    hideGuestsHiddenLayout()
                }else if (isGuestsTabSelected && !shouldShowGuests) {
                    hideOutOfPeopleLayout()
                    showGuestsHiddenLayout()
                }else if (isMembersTabSelected) {
                    hideOutOfPeopleLayout()
                }
                readMainUserInfo()
                UserObject.userPreferencesChangedLiveData.postValue(false)
            }
        }

        ScreenRouter.saveScreenInfoToFirebase(this, this::class.java.simpleName)
    }

    private fun setupToolBar() {

        toolbar = findViewById(R.id.tool_bar_browse_profile)
        segmentedButtonGroup = findViewById(R.id.segment_control_group)
        memberButton = findViewById(R.id.member_button)
        val typeface = ResourcesCompat.getFont(this, R.font.font_nunitosans_extrabold)
        memberButton.setTypeface(typeface)
        membersBrowseTextView = findViewById(R.id.browse_text_view)
        guestButton = findViewById(R.id.guest_button)
        guestButton.setTypeface(typeface)

        if (!AccountPreferences.getInstance(this).getBooleanValue(isAMember, true)) {

            segmentedButtonGroup.visibility = View.GONE
            membersBrowseTextView.visibility = View.VISIBLE
        }

        segmentedButtonGroup.setOnClickedButtonListener {
            if (it == 1) {

                //Slided the segmented button to Guest button
                isGuestsTabSelected = true
                isMembersTabSelected = false
                segmentedButtonGroup.backgroundColor = ContextCompat.getColor(this, R.color.guests_color)

                showGuests()
            } else {

                isGuestsTabSelected = false
                isMembersTabSelected = true
                segmentedButtonGroup.backgroundColor = ContextCompat.getColor(this, R.color.members_color)

                showMembers()
            }
        }
    }

    private fun hideToolbar() {
        segmentedButtonGroup.visibility = View.GONE
        membersBrowseTextView.visibility = View.VISIBLE
    }

    private fun showToolbar() {
        segmentedButtonGroup.visibility = View.VISIBLE
        membersBrowseTextView.visibility = View.GONE
    }

    private fun startPreferencesFragment() {
        val intent = SettingsActivity.newIntent(this)
        intent.putExtra(Constants.PREFERENCES_FRAGMENT_HOME_PROFILE_ACTIVITY, true)
        startActivity(intent)
    }

    private fun startTeleportFragment() {
        val intent = SettingsActivity.newIntent(this)
        intent.putExtra(Constants.PREFERENCES_FRAGMENT_TELEPORT_FRAGMENT, true)
        startActivity(intent)
    }

    @SuppressLint("ClickableViewAccessibility")
    private fun hideLoading() {

        isLoadingProfiles = false

        progressBar.visibility = View.GONE
        progressTextView.visibility = View.GONE

        likeButton.visibility = View.VISIBLE
        disLikeButton.visibility = View.VISIBLE

        likeButton.isEnabled = true
        disLikeButton.isEnabled = true
        segmentedButtonGroup.isEnabled = true
        scrollBar.setOnTouchListener { _, _ -> false}

        firstPhotoImageView.visibility = View.VISIBLE
        userNameTextView.visibility = View.VISIBLE
        heightTextView.visibility = View.VISIBLE
        topHeelsIcon.visibility = View.VISIBLE
        ageTextView.visibility = View.VISIBLE
        distanceTextView.visibility = View.VISIBLE
        userLocationTextView.visibility = View.VISIBLE
        locationIconImageView.visibility = View.VISIBLE
        ageHeaderTextView.visibility = View.VISIBLE
        professionTextView.visibility = View.VISIBLE
        professionHeaderTextView.visibility = View.VISIBLE
    }

    @SuppressLint("ClickableViewAccessibility")
    private fun showLoading() {

        scrollBar.scrollTo(0, 0)

        hideOutOfPeopleLayout()
        hideGetNearbyUsersErrorLayout()

        isLoadingProfiles = true

        progressBar.visibility = View.VISIBLE
        progressBar.isIndeterminate = true
        progressTextView.visibility = View.VISIBLE

        firstPhotoImageView.background = ColorDrawable(resources.getColor(R.color.profile_loading_background))
        firstPhotoImageView.visibility = View.INVISIBLE
        userNameTextView.visibility = View.INVISIBLE
        heightTextView.visibility = View.INVISIBLE
        topHeelsIcon.visibility = View.INVISIBLE
        ageTextView.visibility = View.INVISIBLE
        distanceTextView.visibility = View.INVISIBLE
        userLocationTextView.visibility = View.INVISIBLE
        locationIconImageView.visibility = View.INVISIBLE
        ageHeaderTextView.visibility = View.INVISIBLE
        professionTextView.visibility = View.INVISIBLE
        professionHeaderTextView.visibility = View.INVISIBLE

        hideHeightVerifiedBadge()

        likeButton.isEnabled = false
        disLikeButton.isEnabled = false
        segmentedButtonGroup.isEnabled = false
        scrollBar.setOnTouchListener { _, _ -> true }
    }

    private fun retrieveProfilePhotoAndSaveToPrefs() {
        spacesImagesViewModel.getImageUrl("$mainUserId/${Constants.photoFileName1}") { url, _ ->
            url?.let {
                AccountPreferences.getInstance(this).setValue(Constants.userProfilePhotoUrl, it)
            }
        }
    }

    private fun onButtonGhostPressed() {
        val appLogic = AppLogic(mContext, mainUserId, previousMatchUserId)
        appLogic.reportUser(shouldShowUnmatchButton = false)
    }

    private fun readMainUserInfo() {
        hideLocationLockedLayout()
        if (UserObject.shouldFetchUserFromServer()) {
            firebaseDatabaseUtil?.readMainUserInfoFromFirebaseFirestore(object : FirebaseRetrieveSingleUserListenerInterfaceFirestore {
                override fun onSuccess(user: User) {
                    mainUserInfo = user
                    shouldShowGuests = mainUserInfo?.showGuests ?: true
                    userCurrentCity = mainUserInfo?.city?.toString() ?: ""
                    mainUserInfo?.let { UserObject.setUserDataStore(it, mainUserId)}
                    readUserLocationAndFetchNearbyUsers()
                }
                override fun onFailure() {
                    Timber.e("failed reading main user info in browse profiles")
                }
            })
        }else {
            mainUserInfo = UserObject.user
            shouldShowGuests = mainUserInfo?.showGuests ?: true
            userCurrentCity = mainUserInfo?.city?.toString() ?: ""
            readUserLocationAndFetchNearbyUsers()
        }
        retrieveProfilePhotoAndSaveToPrefs()
    }

    private fun readUserLocationAndFetchNearbyUsers() {

        Timber.d("Read user location and fetch nearby users")

        resetMemoryCachedProfiles()

        showLoading()

        Handler(Looper.getMainLooper()).postDelayed({

            hideBrowsingInCityView()

            var lat = AccountPreferences.getInstance(mContext).getDoubleValue(Constants.latitude, 0.0)
            var long = AccountPreferences.getInstance(mContext).getDoubleValue(Constants.longitude, 0.0)

            UserObject.selectedUnlockedLocation?.let {
                lat = it.latitude
                long = it.longitude
                showBrowsingInCityView()
                hideLocationLockedLayout()
            }

            if (lat == 0.0 || long == 0.0) {

                setLocation()

                val localBroadcastManager = LocalBroadcastManager.getInstance(mContext)
                localBroadcastManager.registerReceiver(locationBroadcastReceiver, IntentFilter(BackgroundLocationIntentService.LOCATION_FETCH_STATUS_ACTION))
            } else {

                readNearbyUsers(lat, long)
            }
        }, 200)
    }

    private fun readNearbyUsers(searchLat: Double, searchLong: Double) {
        val distanceFromPrefs = AccountPreferences.getInstance(applicationContext).getIntValue(Constants.distance, Constants.defaultDistance)

        if (mainUserId.isEmpty()) {
            mainUserId = AccountPreferences.getInstance(this).getStringValue(Constants.firebaseUserId, "")
        }

        Timber.d("Getting users for distance: $distanceFromPrefs")

        NearByUsers.getNearByUsers(mainUserId, searchLat, searchLong, distanceFromPrefs) { nearbyUsersList, apiStatus ->
            if (apiStatus == GetUsersResponse.SUCCESS) {
                if (nearbyUsersList != null && nearbyUsersList.isNotEmpty()) {
                    Timber.d("Received users size: ${nearbyUsersList.size}")
                    for (nearByUser in nearbyUsersList) {
                        val nearByUserUid = nearByUser.uid
                        if (nearByUser.isAMember == true) {
                            if (!usersMembersMap.containsKey(nearByUserUid)) {
                                usersMembersMap[nearByUserUid] = nearByUser
                                usersMembersKeysArrayList.add(nearByUserUid)
                            } else {
                                Timber.d("Not a valid user: Key already exists")
                            }
                        } else {
                            if (!usersGuestsMap.containsKey(nearByUserUid)) {
                                usersGuestsMap[nearByUserUid] = nearByUser
                                usersGuestsKeysArrayList.add(nearByUserUid)
                            } else {
                                Timber.d("Not a valid user: Key already exists")
                            }
                        }
                    }

                    hideLoading()
                    hideOutOfPeopleLayout()

                    if (isMembersTabSelected) {

                        showNextProfile(USERLIKINGSTATUS.NOTDECIDED, usersMembersKeysArrayList)

                    } else {

                        showNextProfile(USERLIKINGSTATUS.NOTDECIDED, usersGuestsKeysArrayList)
                    }
                } else {
                    shouldFetchUsers = false
                    showOutOfPeopleLayout()
                }
            } else {
                Timber.e("Get users API Failed, Showing error layout")
                showGetNearbyUsersErrorLayout()
            }
        }
    }

    private fun removeFirstUserItem(userKey: String?) {

        if (isMembersTabSelected) {

            usersMembersKeysArrayList.removeAt(0)
            usersMembersMap.remove(userKey)
        } else {

            usersGuestsKeysArrayList.removeAt(0)
            usersGuestsMap.remove(userKey)
        }
    }

    private fun onLikeButtonClicked() {
        tempLikeCounterInMemory++
        if (!reviewShown && tempLikeCounterInMemory == 4) {
            AppReview().showEnjoyingDateUpDialog(this)
            AccountPreferences.getInstance(this).setValue(Constants.reviewShown, true)
        }
        if(!UserObject.hasPlusOrSelect) { // check if user is free user
            if (!freeTrailShown && (deviceLikeCounter == 7 || deviceLikeCounter == 12 || deviceLikeCounter == 18)) {
                startSubscriptionActivity()
                if (deviceLikeCounter == 18) {
                    AccountPreferences.getInstance(this).setValue(Constants.freeTrailShown, true)
                }
            }
            if (deviceLikeCounter >= likesThreshold) {
                // Should show subscription screen
                val subIntent = SubscriptionActivity.newIntent(this)
                subIntent.putExtra(
                    SubscriptionActivity.SUBSCRIPTION_ORIGIN,
                    SubscriptionActivity.OUT_OF_LIKES
                )
                startActivity(subIntent.launchModeWithSingleTop())
            }else {
                likeUser()
                deviceLikeCounter++
                NumLikesRestriction.updateLikeCounterOnDeviceAndServer(this)
            }
        }else {
            likeUser()
        }
    }

    private fun likeUser() {
        if (isMembersTabSelected) {
            usersMembersListCounter.plus(1)
            showNextProfile(USERLIKINGSTATUS.LIKE, usersMembersKeysArrayList)
        } else {
            usersGuestsListCounter.plus(1)
            showNextProfile(USERLIKINGSTATUS.LIKE, usersGuestsKeysArrayList)
        }
        AnalyticsTrackingService.logEvent(this, LIKE_USER)
    }

    private fun onDisLikeButtonClicked() {

        if (isMembersTabSelected) {

            usersMembersListCounter.plus(1)

            showNextProfile(USERLIKINGSTATUS.DISLIKE, usersMembersKeysArrayList)
        } else {

            usersGuestsListCounter.plus(1)

            showNextProfile(USERLIKINGSTATUS.DISLIKE, usersGuestsKeysArrayList)
        }

        AnalyticsTrackingService.logEvent(this, DISLIKE_USER)
    }

    private fun showNextProfile(previousUserLikeStatus: USERLIKINGSTATUS, usersKeysArrayList: ArrayList<String?>) {

        if (previousUserLikeStatus != USERLIKINGSTATUS.NOTDECIDED) {

            if (isMembersTabSelected) {

                if (usersMembersKeysArrayList.isNotEmpty()) {
                    removeFirstUserItem(previousMatchUserId)
                } else {
                    if (!shouldFetchUsers) {
                        showOutOfPeopleLayout()
                    }
                }
            } else {

                if (usersGuestsKeysArrayList.isNotEmpty()) {
                    removeFirstUserItem(previousMatchUserId)
                } else {
                    if (!shouldFetchUsers) {
                        showOutOfPeopleLayout()
                    }
                }
            }
        } else if (previousUserLikeStatus == USERLIKINGSTATUS.NOTDECIDED) {

            if (isMembersTabSelected && usersMembersKeysArrayList.isEmpty()) {

                if (!shouldFetchUsers) {
                    showOutOfPeopleLayout()
                }
            } else if (isGuestsTabSelected && usersGuestsKeysArrayList.isEmpty()) {

                if (!shouldFetchUsers) {
                    showOutOfPeopleLayout()
                }
            }
        }
        lottieAnimationView.removeAllAnimatorListeners()
        lottieAnimationView.invalidate()

        clearViewData()

        if (usersKeysArrayList.isEmpty()) {

            if ("" != previousMatchUserId) {

                when (previousUserLikeStatus) {
                    USERLIKINGSTATUS.LIKE -> {

                        firebaseDatabaseUtil?.likeUser(mainUserId, previousMatchUserId)

                        checkForMutualMatch(mainUserId, previousMatchUserId, true)

                        previousMatchUserId = ""
                    }
                    USERLIKINGSTATUS.DISLIKE -> {

                        firebaseDatabaseUtil?.disLikeUser(mainUserId, previousMatchUserId)
                        previousMatchUserId = ""

                        showOutOfPeopleLayout()
                    }
                    else -> {

                        previousMatchUserId = ""
                        showOutOfPeopleLayout()
                    }
                }
            } else {
                showOutOfPeopleLayout()
            }
        } else {

            var userKey: String? = null
            var user: NearByUser? = null

            if (isMembersTabSelected) {

                userKey = usersKeysArrayList[usersMembersListCounter]
                user = usersMembersMap[userKey]

            } else {

                userKey = usersKeysArrayList[usersGuestsListCounter]
                user = usersGuestsMap[userKey]
            }

            firstPhotoRef = "$userKey/${Constants.photoFileName1}"
            secondPhotoRef = "$userKey/${Constants.photoFileName2}"
            thirdPhotoRef = "$userKey/${Constants.photoFileName3}"
            fourthPhotoRef = "$userKey/${Constants.photoFileName4}"
            fifthPhotoRef = "$userKey/${Constants.photoFileName5}"
            sixthPhotoRef = "$userKey/${Constants.photoFileName6}"

            if (previousUserLikeStatus == USERLIKINGSTATUS.LIKE) {

                lottieAnimationView.elevation = 10f
                lottieAnimationView.setAnimation(R.raw.likeanimation)
                lottieAnimationView.playAnimation()

                lottieAnimationView.addAnimatorListener(object : Animator.AnimatorListener {
                    override fun onAnimationRepeat(animation: Animator) {
                        Timber.d("animation repeated")
                    }

                    override fun onAnimationCancel(animation: Animator) {
                        Timber.d("animation cancelled")
                        showUserData(user, userKey)
                    }

                    override fun onAnimationStart(animation: Animator) {
                        //Timber.d("animation started like")
                    }

                    override fun onAnimationEnd(animation: Animator) {
                        //Timber.d("animation ended")
                        showUserData(user, userKey)
                    }
                })

                if (!TextUtils.isEmpty(previousMatchUserId)) {

                    firebaseDatabaseUtil?.likeUser(mainUserId, previousMatchUserId)

                    //There is a match between these two users, send the match details to firebase
                    //there is one more place we are doing the same above, if we update here, update there as well
                    checkForMutualMatch(mainUserId, previousMatchUserId, false)

                    previousMatchUserId = ""
                }
            } else if (previousUserLikeStatus == USERLIKINGSTATUS.DISLIKE) {

                lottieAnimationView.elevation = 10f
                lottieAnimationView.setAnimation(R.raw.dislikelikeanimation)
                lottieAnimationView.playAnimation()

                lottieAnimationView.addAnimatorListener(object : Animator.AnimatorListener {
                    override fun onAnimationRepeat(animation: Animator) {
                        Timber.d("animation repeated")
                    }

                    override fun onAnimationCancel(animation: Animator) {
                        Timber.d("animation cancelled")
                        showUserData(user, userKey)
                    }

                    override fun onAnimationStart(animation: Animator) {
                        Timber.d("animation started dislike")
                    }

                    override fun onAnimationEnd(animation: Animator) {
                        Timber.d("animation ended")
                        showUserData(user, userKey)
                    }
                })


                if (!TextUtils.isEmpty(previousMatchUserId)) {

                    firebaseDatabaseUtil?.disLikeUser(mainUserId, previousMatchUserId)

                    previousMatchUserId = ""

                }
            } else if (previousUserLikeStatus == USERLIKINGSTATUS.NOTDECIDED) {

                showUserData(user, userKey)
            }
        }
    }

    private fun showUserData(user: NearByUser?, userKey: String?) {

        try {

            var iceBreaker1Questions: Any = ""

            var iceBreaker1Question = ""
            var iceBreaker1Answer = ""
            var iceBreaker2Question = ""
            var iceBreaker2Answer = ""

            if (user?.iceBreaker1 != null) {
                if (user.iceBreaker1 is Map<*, *>) {

                    iceBreaker1Questions = user.iceBreaker1

                    for (key in iceBreaker1Questions.keys) {
                        iceBreaker1Question = key.toString()
                    }

                    iceBreaker1Answer = iceBreaker1Questions[iceBreaker1Question].toString()
                }
            }

            var iceBreaker2Questions: Any = ""
            if (user?.iceBreaker2 != null) {
                if (user.iceBreaker2 is Map<*, *>) {
                    iceBreaker2Questions = user.iceBreaker2

                    for (key2 in iceBreaker2Questions.keys) {
                        iceBreaker2Question = key2.toString()
                    }

                    iceBreaker2Answer = iceBreaker2Questions[iceBreaker2Question].toString()
                }
            }

            if (iceBreaker1Question.isNotEmpty() && iceBreaker1Answer.isNotEmpty()) {

                iceBreaker1Layout.visibility = View.VISIBLE
                iceBreakerQuestion1TextView.text = iceBreaker1Question
                iceBreakerAnswer1TextView.text = iceBreaker1Answer
            } else {

                iceBreaker1Layout.visibility = View.GONE
            }

            if (iceBreaker2Question.isNotEmpty() && iceBreaker2Answer.isNotEmpty()) {

                iceBreaker2Layout.visibility = View.VISIBLE
                iceBreakerQuestion2TextView.text = iceBreaker2Question
                iceBreakerAnswer2TextView.text = iceBreaker2Answer
            } else {

                iceBreaker2Layout.visibility = View.GONE
            }


            setPhotoIntoImageViewWithIceBreaker(this, firstPhotoRef, firstPhotoImageView, firstIcebreakerBackgroundImageView)

            setPhotoIntoImageViewWithIceBreaker(this, secondPhotoRef, secondPhotoImageView, secondIcebreakerBackgroundImageView)

            setPhotoIntoImageViewWithIceBreaker(this, thirdPhotoRef, thirdPhotoImageView, null)

            setPhotoIntoImageViewWithIceBreaker(this, fourthPhotoRef, fourthPhotoImageView, null)

            setPhotoIntoImageViewWithIceBreaker(this, fifthPhotoRef, fifthPhotoImageView, null)

            setPhotoIntoImageViewWithIceBreaker(this, sixthPhotoRef, sixthPhotoImageView, null)

            val sdf = SimpleDateFormat("MM/dd/yyyy", Locale.US)
            val date = sdf.parse(user?.dob.toString())

            var school = ""

            val userGender = user?.gender ?: ""

            if (mainUserGender == userGender) {

                hideHeelsIcon()
            } else if (mainUserGender == GenderType.woman.toString() && userGender == GenderType.man.toString()) {

                // get mainuser height in inches
                // get other user height in inches
                // pass these two as parameters to HeelsLogic class
                // get back top-badge-id, bottom-badge-id and text copy from HeelsLogic class
                // assign them to these below views
                val mainUserHeightInInches = AccountPreferences.getInstance(mContext).getDoubleValue(Constants.height, 0.0).toString().getDoubleValue()?.getIntValue()
                val otherUserHeightInInches = user?.height.toString().getDoubleValue()?.getIntValue()

                if (otherUserHeightInInches != null &&
                        mainUserHeightInInches != null) {

                    HeelsLogic.getBadgeAndText(otherUserHeightInInches, mainUserHeightInInches, user?.name.toString().trim()) { topHeelsIconId, bottomHeelsIconId, bottomHeelsText, shouldShowComponent ->

                        if (shouldShowComponent) {

                            showHeelsIcon()

                            topHeelsIcon.setImageResource(topHeelsIconId)
                            bottomHeelsIcon.setImageResource(bottomHeelsIconId)
                            bottomHeelsTextViewPreview.text = bottomHeelsText
                        } else {

                            hideHeelsIcon()
                        }
                    }
                } else {

                    hideHeelsIcon()
                }
            } else {

                hideHeelsIcon()
            }

            userNameTextView.text = user?.name.toString()

            if (user?.isHeightVerified == true) {

                showHeightVerifiedBadge()
            } else {

                hideHeightVerifiedBadge()
            }

            val userHeightInInches = Utils.heightInFeetFromInchesWithQuotes(user?.height.toString().toDouble())
            heightTextView.text = userHeightInInches


            ageTextView.text = Utils.getAge(date).toString() + " yrs"

            if (!user?.school.isNullOrEmpty()) {
                school = user?.school.toString()
            }

            if (!user?.school2.isNullOrEmpty()) {
                school = school + "\n" + user?.school2.toString()
            }

            if (user?.profession.isNullOrEmpty()) {
                hideProfession()
            } else {
                showProfession()
                professionTextView.text = user?.profession.toString()
            }

            if (user?.school.isNullOrEmpty()) {
                hideSchool()
            } else {
                showSchool()
                educationTextView.text = school
            }

            if (user?.homeTown.isNullOrEmpty()) {
                hideHomeTown()
            } else {
                showHomeTown()
                hometownTextView.text = user?.homeTown.toString()
            }

            val userCity = user?.city
            val userState = user?.state
            var location = ""
            if (userCity != null && userState != null) {
                location = "$userCity, $userState"
            }

            val distance = user?.distance

            if (distance != null) {
                if (distance == 0 || distance == 1) {
                    if (location != "") {
                        userLocationTextView.text = location
                        distanceTextView.text = "<1 mile away"
                    }else {
                        distanceTextView.text = "<1 mile away"
                    }
                } else {
                    if (location != "") {
                        userLocationTextView.text = location
                        distanceTextView.text = "$distance miles away"
                    }else {
                        distanceTextView.text = "$distance miles away"
                    }
                }
            }

            previousMatchUserId = userKey.toString()

            user?.uid?.let { id ->
                sendDMButton.setOnClickListener {
                    if (UserObject.isDateUpSelectUser == true) {
                        val dialog = SendMessageDialogFragment.newInstance(id, firstPhotoRef, user.name.toString(), spacesImagesViewModel) {
                            onLikeButtonClicked()
                        }
                        dialog.show(supportFragmentManager, "SendMessageDialog")
                    } else {
                        startSubscriptionActivity()
                    }
                }
            }
        } catch (e: Exception) {
            Timber.e("exception in showing user profile: $e")
        }
    }

    private fun checkForMutualMatch(mainUserFirebaseId: String, otherUserFirebaseId: String, isLastProfile: Boolean) {

        firebaseDatabaseUtil?.getLikedByUsers(object : FirebaseRetrieveLikedByUsersListener {
            override fun onSuccess(isMutualMatch: Boolean) {
                if (isMutualMatch) {

                    AnalyticsTrackingService.logEvent(this@BrowseProfilesActivity, MUTUAL_MATCH)

                    val firebaseDatabaseReference = FirebaseDatabase.getInstance().getReference(conversations).push()
                    val chatId = firebaseDatabaseReference.key

                    firebaseDatabaseUtil?.matchUsers(mainUserFirebaseId, otherUserFirebaseId, chatId)

                    val intent = Intent(mContext, MutualMatchActivity::class.java)
                    intent.putExtra(Constants.firebaseUserId, otherUserFirebaseId)
                    intent.putExtra(Constants.conversationId, chatId)
                    startActivity(intent)

                } else if (isLastProfile) {
                    showOutOfPeopleLayout()
                }
            }

            override fun onFailure() {
                if (isLastProfile) {
                    showOutOfPeopleLayout()
                }
            }
        }, mainUserFirebaseId, otherUserFirebaseId)
    }

    private fun resetMemoryCachedProfiles() {
        usersMembersMap = LinkedHashMap()
        usersMembersKeysArrayList = ArrayList()
        usersMembersListCounter = 0

        usersGuestsMap = LinkedHashMap()
        usersGuestsKeysArrayList = ArrayList()
        usersGuestsListCounter = 0

        previousMatchUserId = ""
    }

    private fun clearViewData() {

        scrollBar.scrollTo(0, 0)

        userNameTextView.text = ""
        heightTextView.text = ""
        ageTextView.text = ""
        distanceTextView.text = ""
        userLocationTextView.text = ""
        professionTextView.text = ""
        educationTextView.text = ""
        hometownTextView.text = ""
        iceBreakerQuestion1TextView.text = ""
        iceBreakerAnswer1TextView.text = ""
        iceBreakerQuestion2TextView.text = ""
        iceBreakerAnswer2TextView.text = ""

        firstPhotoImageView.setImageResource(R.color.white)
        secondPhotoImageView.setImageResource(R.color.white)
        firstIcebreakerBackgroundImageView.setImageResource(R.color.white)
        secondIcebreakerBackgroundImageView.setImageResource(R.color.white)
        topHeelsIcon.setImageResource(R.color.white)

        thirdPhotoImageView.setImageResource(R.color.white)
        hidePhotoImageView(thirdPhotoImageView)
        fourthPhotoImageView.setImageResource(R.color.white)
        hidePhotoImageView(fourthPhotoImageView)
        fifthPhotoImageView.setImageResource(R.color.white)
        hidePhotoImageView(fifthPhotoImageView)
        sixthPhotoImageView.setImageResource(R.color.white)
        hidePhotoImageView(sixthPhotoImageView)

        hideHeightVerifiedBadge()
    }

    enum class USERLIKINGSTATUS {
        LIKE,
        DISLIKE,
        NOTDECIDED
    }

    private fun hideProfession() {
        professionHeaderTextView.visibility = View.GONE
        professionTextView.visibility = View.GONE

        invalidateLayout()
    }

    private fun showProfession() {
        professionHeaderTextView.visibility = View.VISIBLE
        professionTextView.visibility = View.VISIBLE

        invalidateLayout()
    }

    private fun hideSchool() {
        educationTextViewTitle.visibility = View.GONE
        educationTextView.visibility = View.GONE

        invalidateLayout()
    }

    private fun showSchool() {
        educationTextViewTitle.visibility = View.VISIBLE
        educationTextView.visibility = View.VISIBLE

        invalidateLayout()
    }

    private fun hideHomeTown() {
        hometownTextViewHeader.visibility = View.GONE
        hometownTextView.visibility = View.GONE

        invalidateLayout()
    }

    private fun showHomeTown() {
        hometownTextViewHeader.visibility = View.VISIBLE
        hometownTextView.visibility = View.VISIBLE

        invalidateLayout()
    }

    private fun hidePhotoImageView(photoView: ImageView) {

        photoView.visibility = View.GONE

        invalidateLayout()
    }

    private fun showPhotoImageView(photoView: ImageView) {

        photoView.visibility = View.VISIBLE

        invalidateLayout()
    }

    private fun invalidateLayout() {
        activityBrowseProfilesConstraintLayout.invalidate()
    }

    private fun setImageViewHeightBasedOnDeviceWidth() {
        val height = AppUtils.getHeightForImages()

        val firstImageLayoutParams = firstPhotoImageView.layoutParams as? ConstraintLayout.LayoutParams
        firstImageLayoutParams?.height = height
        firstImageLayoutParams?.let { params ->
            firstPhotoImageView.layoutParams = params
        }

        val firstIceBackgroundImage = iceBreaker1Layout.layoutParams as? ConstraintLayout.LayoutParams
        firstIceBackgroundImage?.height = height
        firstIceBackgroundImage?.let { params ->
            iceBreaker1Layout.layoutParams = params
        }

        val secondImageLayoutParams = secondPhotoImageView.layoutParams as? ConstraintLayout.LayoutParams
        secondImageLayoutParams?.height = height
        secondImageLayoutParams?.let { params ->
            secondPhotoImageView.layoutParams = params
        }

        val secondIceBackgroundImage = iceBreaker2Layout.layoutParams as? ConstraintLayout.LayoutParams
        secondIceBackgroundImage?.height = height
        secondIceBackgroundImage?.let { params ->
            iceBreaker2Layout.layoutParams = params
        }

        val thirdImageLayoutParams = thirdPhotoImageView.layoutParams as? ConstraintLayout.LayoutParams
        thirdImageLayoutParams?.height = height
        thirdImageLayoutParams?.let { params ->
            thirdPhotoImageView.layoutParams = params
        }

        val fourthImageLayoutParams = fourthPhotoImageView.layoutParams as? ConstraintLayout.LayoutParams
        fourthImageLayoutParams?.height = height
        fourthImageLayoutParams?.let { params ->
            fourthPhotoImageView.layoutParams = params
        }

        val fifthImageLayoutParams = fifthPhotoImageView.layoutParams as? ConstraintLayout.LayoutParams
        fifthImageLayoutParams?.height = height
        fifthImageLayoutParams?.let { params ->
            fifthPhotoImageView.layoutParams = params
        }

        val sixthImageLayoutParams = sixthPhotoImageView.layoutParams as? ConstraintLayout.LayoutParams
        sixthImageLayoutParams?.height = height
        sixthImageLayoutParams?.let { params ->
            sixthPhotoImageView.layoutParams = params
        }
    }

    private fun setPhotoIntoImageViewWithIceBreaker(activity: Activity,
                                                    spacesPath: String,
                                                    actualImageView: ImageView,
                                                    icebreakerImageView: ImageView?) {

        ImageLoaderModule.loadImageIntoImageViewWithLoadingAndCallback(applicationContext, activity, spacesImagesViewModel, spacesPath, actualImageView, onSuccess = {
            showPhotoImageView(actualImageView)
        }, onFailure = {
            hidePhotoImageView(actualImageView)
        })

        if (icebreakerImageView != null) {
            ImageLoaderModule.loadImageIntoImageViewWithLoadingAndCallback(applicationContext, activity, spacesImagesViewModel, spacesPath, icebreakerImageView, onSuccess = {
                showPhotoImageView(icebreakerImageView)
            }, onFailure = {
                hidePhotoImageView(icebreakerImageView)
            })
        }
    }

    private fun showGuestsHiddenLayout() {
        if (UserObject.isUserLocationUnlocked == false && UserObject.selectedUnlockedLocation == null) {
            hideGuestsHiddenLayout()
        } else {
            val interestedIn = AccountPreferences.getInstance(this).getStringValue(Constants.interestedIn, "")

            guestHiddenFragment.visibility = View.VISIBLE

            val guestsHiddenImageView = guestHiddenFragment.findViewById<ImageView>(R.id.guest_hidden_image_view)
            val enableButton = guestHiddenFragment.findViewById<Button>(R.id.enable_guest_matching_button)

            when {
            (mainUserGender == GenderType.man.toString()) and (interestedIn == InterestedInGender.woman.toString()) -> {
                guestsHiddenImageView.setImageResource(R.drawable.ic_tall_man_short_woman)
            }
            (mainUserGender == GenderType.woman.toString()) and (interestedIn == InterestedInGender.man.toString()) -> {
                guestsHiddenImageView.setImageResource(R.drawable.ic_tall_woman_with_short_man)
            }
            (mainUserGender == GenderType.woman.toString()) and (interestedIn == InterestedInGender.woman.toString()) -> {
                guestsHiddenImageView.setImageResource(R.drawable.ic_tall_woman_short_woman)
            }
            (mainUserGender == GenderType.man.toString()) and (interestedIn == InterestedInGender.man.toString()) -> {
                guestsHiddenImageView.setImageResource(R.drawable.ic_tall_man_short_man)
            }
        }

            enableButton.setOnClickListener {
            startPreferencesFragment()
        }

        likeDislikeFrameLayout.visibility = View.GONE
        scrollBar.visibility = View.GONE
        lottieAnimationView.visibility = View.GONE
        }
    }

    private fun hideGuestsHiddenLayout() {
        hideOutOfPeopleLayout()

        guestHiddenFragment.visibility = View.GONE
        likeDislikeFrameLayout.visibility = View.VISIBLE
        scrollBar.visibility = View.VISIBLE
        lottieAnimationView.visibility = View.VISIBLE
    }

    private fun showGuests() {
        if (!shouldShowGuests) {

            showGuestsHiddenLayout()
        } else {

            hideOutOfPeopleLayout()

            previousMatchUserId = ""

            showNextProfile(USERLIKINGSTATUS.NOTDECIDED, usersGuestsKeysArrayList)
        }
    }

    private fun showMembers() {

        hideGuestsHiddenLayout()

        showNextProfile(USERLIKINGSTATUS.NOTDECIDED, usersMembersKeysArrayList)
    }

    private fun setOutOfPeopleLayoutListeners() {

        val imageView = layoutOutOfPeople.findViewById<ImageView>(R.id.icon_empty_magnifying_glass_image_view)
        val outOfPeopleTextView = layoutOutOfPeople.findViewById<TextView>(R.id.you_re_out_of_people_text_view)
        val expandSearchTextView = layoutOutOfPeople.findViewById<TextView>(R.id.try_expanding_your_ptext_view)
        val buttonOpenPrefs = layoutOutOfPeople.findViewById<Button>(R.id.button_open_preferences)

        val isMainMember = AccountPreferences.getInstance(this).getBooleanValue(isAMember, false)

        if (!isMainMember) {

            imageView.setImageResource(R.drawable.ic_out_of_members)
            outOfPeopleTextView.text = getString(R.string.blank_out_of_people_activity_you_re_out_of_members_text_view_text)
            expandSearchTextView.text = getString(R.string.blank_out_of_people_for_guest)
        } else {

            if (isMembersTabSelected) {

                imageView.setImageResource(R.drawable.ic_out_of_members)
                outOfPeopleTextView.text = getString(R.string.blank_out_of_people_activity_you_re_out_of_members_text_view_text)
                expandSearchTextView.text = getString(R.string.blank_out_of_people_activity_try_expanding_your_members_view_text)
            } else {
                imageView.setImageResource(R.drawable.ic_out_of_guests)
                outOfPeopleTextView.text = getString(R.string.blank_out_of_people_activity_you_re_out_of_guests_text_view_text)
                expandSearchTextView.text = getString(R.string.blank_out_of_people_activity_try_expanding_your_guests_view_text)
            }
        }

        buttonOpenPrefs.setOnClickListener {
            startPreferencesFragment()
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)

        if (requestCode == enableLocationInSettingsRequestCode) {

            setLocation()
        }
    }


    private fun showHeelsIcon() {

        topHeelsIcon.visibility = View.VISIBLE
        bottomHeelsSectionLayout.visibility = View.VISIBLE
    }

    private fun hideHeelsIcon() {

        topHeelsIcon.visibility = View.INVISIBLE
        bottomHeelsSectionLayout.visibility = View.GONE
    }

    private fun setLocation() {

        val managePermissions = ManagePermissions(this, BackgroundLocationIntentService.locationPermissionsList, BackgroundLocationIntentService.locationPermissionRequestCode)

        if (managePermissions.isPermissionGranted()) {

            saveLocationDetails()

            hideEnableLocationLayout()
        } else {
            if (managePermissions.isPermissionDenied()) {

                handleDeniedLocationPermission()
            } else {

                managePermissions.checkPermissions()

                hideEnableLocationLayout()
            }
        }
    }

    private fun handleDeniedLocationPermission() {

        showEnableLocationLayout()
    }

    private fun saveLocationDetails() {

        val intent = Intent(this, BackgroundLocationIntentService::class.java)
        BackgroundLocationIntentService.enqueueWork(this, intent)
    }

    private fun checkIfUserIsLocationRestricted(callback: (Boolean, LocationLocationResponse?) -> Unit) {

        val managePermissions = ManagePermissions(this, BackgroundLocationIntentService.locationPermissionsList, BackgroundLocationIntentService.locationPermissionRequestCode)

        if (managePermissions.isPermissionGranted()) {

            val accountPreferences = AccountPreferences.getInstance(this)
            val latPref = accountPreferences.getDoubleValue(Constants.latitude, 0.0)
            val longPref = accountPreferences.getDoubleValue(Constants.longitude, 0.0)
            val city = accountPreferences.getStringValue(Constants.city, "NA")
            val isUserLocationUnlocked = AccountPreferences.getInstance(this).getBooleanValue(Constants.isUserLocationUnlocked, false)

            if (isUserLocationUnlocked) {
                callback(false, null)
            } else {
                val lockedLocationUtil = LockedLocationUtil(this)
                if (latPref == 0.0 || longPref == 0.0) {
                    val backgroundLocationIntentService = BackgroundLocationIntentService()
                    backgroundLocationIntentService.forceGetLatAndLongValues(this) { lat, long ->
                        if (lat != null && long != null) {
                            lockedLocationUtil.isUserLocationUnlocked(lat, long, city, mainUserId) { isUserLocationUnlocked, locationResponse ->
                                callback(isUserLocationUnlocked, locationResponse)
                            }
                        } else {
                            lockedLocationUtil.isUserLocationUnlocked(latPref, longPref, city, mainUserId) { isUserLocationUnlocked, locationResponse ->
                                callback(isUserLocationUnlocked, locationResponse)
                            }
                        }
                    }
                } else {
                    lockedLocationUtil.isUserLocationUnlocked(latPref, longPref, city, mainUserId) { isUserLocationUnlocked, locationResponse ->
                        callback(isUserLocationUnlocked, locationResponse)
                    }
                }
            }
        }
    }

    var locationBroadcastReceiver: BroadcastReceiver = object : BroadcastReceiver() {

        override fun onReceive(context: Context?, intent: Intent?) {

            if (intent != null) {
                val status = intent.getBooleanExtra(BackgroundLocationIntentService.LOCATION_FETCH_RESULT, false)
                if (status) {
                    hideUserErrorView()
                    readMainUserInfo()
                } else {
                    showUserErrorView()
                }
            } else {
                hideUserErrorView()
                readMainUserInfo()
            }
        }
    }

    private fun showEnableLocationLayout() {

        hideLoading()

        layoutEnableLocation.visibility = View.VISIBLE
        scrollBar.visibility = View.GONE

        val settingsButton = layoutEnableLocation.findViewById<Button>(R.id.button_open_settings)

        settingsButton?.setOnClickListener {
            startActivityForResult(Intent().apply {
                action = Settings.ACTION_APPLICATION_DETAILS_SETTINGS
                data = Uri.fromParts("package", packageName, null)
            }, enableLocationInSettingsRequestCode)
        }
    }

    private fun showHeightVerifiedBadge() {

        heightVerifiedBadge.visibility = View.VISIBLE
        heightVerifiedTextView.visibility = View.VISIBLE

        if (mainUserInfo?.isHeightVerified == false) {
            heightVerifiedTextView.paint?.isUnderlineText = true
            heightVerifiedTextView.setOnClickListener {
                val intent = HeightVerificationScan.newIntent(this).launchModeWithSingleTop()
                startActivity(intent)
            }
        }
    }

    private fun hideHeightVerifiedBadge() {
        heightVerifiedBadge.visibility = View.GONE
        heightVerifiedTextView.visibility = View.GONE
    }

    fun showOutOfPeopleLayout() {
        if (shouldFetchUsers && usersMembersKeysArrayList.isEmpty() && usersGuestsKeysArrayList.isEmpty()) {
            showLoading()
            readUserLocationAndFetchNearbyUsers()
        }else {
            hideLoading()
            layoutOutOfPeople.visibility = View.VISIBLE
            scrollBar.visibility = View.GONE
            setOutOfPeopleLayoutListeners()
        }
    }

    private fun showUserErrorView() {

        hideLoading()
        userErrorLayout.visibility = View.VISIBLE
    }

    private fun hideEnableLocationLayout() {

        layoutEnableLocation.visibility = View.GONE
        scrollBar.visibility = View.VISIBLE
    }

    private fun hideUserErrorView() {

        userErrorLayout.visibility = View.GONE
    }

    private fun hideLocationLockedLayout() {
        scrollBar.visibility = View.VISIBLE
    }

    private fun hideOutOfPeopleLayout() {
        layoutOutOfPeople.visibility = View.GONE
        scrollBar.visibility = View.VISIBLE
    }

    private fun showBrowsingInCityView() {
        browseWithCityIcon.visibility = View.VISIBLE
        browseWithCityIcon.paint.isUnderlineText = true
        browseWithCityIcon.text = UserObject.selectedUnlockedLocation?.city
        browseWithCityTextView.visibility = View.VISIBLE

        browseWithCityIcon.setOnClickListener {
            startTeleportFragment()
        }
    }

    private fun hideBrowsingInCityView() {
        browseWithCityIcon.visibility = View.GONE
        browseWithCityTextView.visibility = View.GONE
    }

    private fun showGetNearbyUsersErrorLayout() {
        val buttonTryAgain = getUsersApiErrorLayout.findViewById<Button>(R.id.button_try_again)
        getUsersApiErrorLayout.visibility = View.VISIBLE
        buttonTryAgain.isEnabled = true
        likeButton.visibility = View.GONE
        disLikeButton.visibility = View.GONE
        buttonTryAgain?.setOnClickListener {
            buttonTryAgain.isEnabled = false
            readUserLocationAndFetchNearbyUsers()
        }
        Timber.e("getNearByUsers API error. Showing error screen to user")
    }

    private fun hideGetNearbyUsersErrorLayout() {
        getUsersApiErrorLayout.visibility = View.GONE

        likeButton.visibility = View.VISIBLE
        disLikeButton.visibility = View.VISIBLE
    }

    private fun checkForSubscriptionsStatusAndUpdateDB() {
        try {
            val billingClientLifecycle = (application as DateUpApplication).billingClientLifecycle
            val billingViewModel: BillingViewModel = ViewModelProvider(this).get(BillingViewModel::class.java)
            billingClientLifecycle.purchases.observe(this) { purchaseList ->
                if (!purchaseList.isNullOrEmpty()) {
                    billingViewModel.checkPurchasesAndAcknowledge(purchaseList)
                }
            }
        } catch (exception: java.lang.Exception) {
            Timber.d("Exception in check subscriptions: $exception")
        }
    }

    private fun startSubscriptionActivity() {
        val subIntent = SubscriptionActivity.newIntent(this)
        val influencerId = AccountPreferences.getInstance(applicationContext).getStringValue(com.dateup.android.utils.Constants.influencerId, "")
        if (influencerId.isNotEmpty()) {
            subIntent.putExtra(SubscriptionActivity.INFLUENCER_FREE_TRAIL, true)
        } else {
            subIntent.putExtra(SubscriptionActivity.FREE_TRAIL, true)
        }
        startActivity(subIntent.launchModeWithSingleTop())
    }

    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        if (usersGuestsMap.isEmpty() && usersMembersMap.isEmpty() && !isLoadingProfiles) {
            readMainUserInfo()
        }
        isBrowseProfilesScreenActive = true
    }


    override fun onDestroy() {
        LocalBroadcastManager.getInstance(this).unregisterReceiver(locationBroadcastReceiver)
        super.onDestroy()
        appLifecycleObserver.detach()
    }
}
