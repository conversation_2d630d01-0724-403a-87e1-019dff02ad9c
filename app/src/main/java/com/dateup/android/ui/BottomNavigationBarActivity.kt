package com.dateup.android.ui

import android.os.Bundle
import android.view.MenuItem
import androidx.core.content.ContextCompat
import com.dateup.android.AccountPreferences
import com.dateup.android.R
import com.dateup.android.ScreenRouter
import com.dateup.android.attribution.AttributionAnalytics
import com.dateup.android.extensions.launchModeWithSingleClearTop
import com.dateup.android.extensions.launchModeWithSingleTop
import com.dateup.android.fcm.DateUpNotificationManager
import com.dateup.android.firebase.FirebaseDatabaseUtil
import com.dateup.android.firebase.FirebaseGetAllLikedByUsersListListener
import com.dateup.android.firebase.FirebaseUnreadStatusListener
import com.dateup.android.models.UserObject
import com.dateup.android.paidVersion.likesYou.LikesYouActivity
import com.dateup.android.paidVersion.likesYou.UpgradeActivity
import com.dateup.android.ui.browseProfiles.BrowseProfilesActivity
import com.dateup.android.ui.chat.ChatActivity
import com.dateup.android.ui.settings.SettingsActivity
import com.dateup.android.utils.AppUtils
import com.dateup.android.utils.Constants
import com.google.android.gms.tasks.OnCompleteListener
import com.google.android.material.bottomnavigation.BottomNavigationView
import com.google.firebase.database.DatabaseReference
import com.google.firebase.database.ValueEventListener
import com.google.firebase.iid.FirebaseInstanceId
import timber.log.Timber

import com.dateup.android.ui.BaseEdgeToEdgeActivity

abstract class BottomNavigationBarActivity : BaseEdgeToEdgeActivity(), BottomNavigationView.OnNavigationItemSelectedListener {

    private lateinit var mBottomNavigationView: BottomNavigationView

    private var unreadMessageStatus = false

    var isChatScreenActive = false
    var isBrowseProfilesScreenActive = true
    var unseenLikesYouProfiles = false

    private var firebaseDatabaseReference: DatabaseReference? = null
    private var firebaseChildEventListener: ValueEventListener? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        setContentView(R.layout.activity_bottom_navigation_bar2)

        mBottomNavigationView = findViewById(R.id.bottom_navigation_bar)
        mBottomNavigationView.inflateMenu(R.menu.navigation)
        mBottomNavigationView.setOnNavigationItemSelectedListener(this)
        mBottomNavigationView.setItemIconSizeRes(R.dimen.profile_icon_height)
        mBottomNavigationView.setBackgroundColor(ContextCompat.getColor(this, R.color.white))
        mBottomNavigationView.itemIconTintList = null
        unreadMessageStatus = false

        mBottomNavigationView.menu.findItem(R.id.navigation_likes_you).isVisible = true

        setOnboardingCompleteFlag()

        ScreenRouter.setLastSeenScreen("", this)

        if (!AppUtils.isNetworkConnected(application)) {

            ScreenRouter.navigateToBlankNoNetworkConnectionScreen(this)
        }

        when {
            intent.getBooleanExtra("MessageListActivity", false) -> {

                isChatScreenActive = true
                setChatBadge()

            }
            intent.getBooleanExtra("MessageListActivityUnMatch", false) -> {

                isChatScreenActive = true
                setChatBadge()

            }
            intent.getBooleanExtra("EditProfileModalActivity", false) -> {

                setChatBadge()

            }
            intent.getBooleanExtra("AccountSettingsUpdateEmailOrPhoneActivity", false) -> {

                setChatBadge()
            }
            else -> {
            }
        }

        firebaseMessageInit()

        setUnreadMessageListener()

        DateUpNotificationManager.clearAllNotifications(applicationContext)

        setVersion()

        getAllLikedByUsersCount {  }
    }

    private fun setVersion() {
        if (!UserObject.isVersionSet) {
            val version = AppUtils.getAppVersion()
            val firebaseDatabaseReference = FirebaseDatabaseUtil(this)
            firebaseDatabaseReference.saveUserInfoToFirebase(Constants.version, version)
            UserObject.isVersionSet = true
        }
    }

    override fun onNavigationItemSelected(item: MenuItem): Boolean {

        mBottomNavigationView.postDelayed({

            var intent = BrowseProfilesActivity.newIntent(this).launchModeWithSingleTop()

            when (item.itemId) {

                R.id.navigation_home -> {

                    isBrowseProfilesScreenActive = true

                    intent = BrowseProfilesActivity.newIntent(this).launchModeWithSingleClearTop()

                    isChatScreenActive = false
                    setChatBadge()
                }
                R.id.navigation_likes_you -> {

                    intent = if (UserObject.hasPlusOrSelect) {
                        LikesYouActivity.newIntent(this).launchModeWithSingleTop()
                    }else {
                        UpgradeActivity.newIntent(this).launchModeWithSingleTop()
                    }

                    isBrowseProfilesScreenActive = false
                    isChatScreenActive = false

                    setChatBadge()
                }
                R.id.navigation_chat -> {

                    intent = ChatActivity.newIntent(this).launchModeWithSingleTop()

                    isBrowseProfilesScreenActive = false
                    isChatScreenActive = true

                    setChatBadge()
                }
                R.id.navigation_settings -> {

                    intent = SettingsActivity.newIntent(this).launchModeWithSingleTop()

                    isBrowseProfilesScreenActive = false
                    isChatScreenActive = false

                    setChatBadge()
                }
            }

            startActivity(intent)
        }, 300)

        return true
    }

    override fun onStart() {
        super.onStart()

        setBottomNavigationBarItem(getBottomNavigationBarItem())

        if (!AppUtils.isNetworkConnected(application)) {

            ScreenRouter.navigateToBlankNoNetworkConnectionScreen(this)
        }
    }

    override fun onPause() {
        super.onPause()

        overridePendingTransition(0, 0)
    }

    abstract fun getBottomNavigationBarItem(): Int

    private fun setBottomNavigationBarItem(itemId: Int) {

        val menuItem = mBottomNavigationView.menu.findItem(itemId)
        menuItem.isChecked = true
    }

    private fun firebaseMessageInit() {
        FirebaseInstanceId.getInstance().instanceId
                .addOnCompleteListener(OnCompleteListener { task ->
                    if (!task.isSuccessful) {

                        Timber.d("firebase token retrieval failed: ${task.exception}")
                        return@OnCompleteListener
                    }

                    // Get new Instance ID token
                    val token = task.result?.token
                    val firebaseDatabaseReference = FirebaseDatabaseUtil(this)
                    token?.let {

                        firebaseDatabaseReference.saveUserInfoToFirebase(Constants.deviceToken, it)
                    }
                })
    }

    private fun setOnboardingCompleteFlag() {

        val isOnboardingComplete = AccountPreferences.getInstance(this).getBooleanValue(Constants.isOnboardingComplete, false)
        if (!isOnboardingComplete) {
            AccountPreferences.getInstance(this).setValue(Constants.isOnboardingComplete, true)
            val firebaseDatabaseUtil = FirebaseDatabaseUtil(this)
            firebaseDatabaseUtil.saveUserInfoToFirebase(Constants.isOnboardingComplete, true)

            // Log onboarding complete with attribution source
            AttributionAnalytics.logOnboardingComplete(this)
        }
    }

    private fun setUnreadMessageListener() {
        val firebaseDatabaseUtil = FirebaseDatabaseUtil(this)
        firebaseDatabaseUtil.readUnreadStatus(object : FirebaseUnreadStatusListener {
            override fun onSuccess(unreadStatus: Boolean, databaseReference: DatabaseReference, listener: ValueEventListener) {

                firebaseDatabaseReference = databaseReference
                firebaseChildEventListener = listener
                unreadMessageStatus = unreadStatus
                setChatBadge()
            }

            override fun onFailure() {
            }
        })
    }

    override fun onDestroy() {
        super.onDestroy()
        removeFirebaseListener()
    }

    private fun removeFirebaseListener() {
        firebaseChildEventListener?.let {
            firebaseDatabaseReference?.removeEventListener(it)
        }
    }

    private fun setChatBadge() {
        this.mBottomNavigationView.getOrCreateBadge(R.id.navigation_chat).isVisible = unreadMessageStatus
    }

    private fun setLikesYouBadge() {
        this.mBottomNavigationView.getOrCreateBadge(R.id.navigation_likes_you).isVisible = unseenLikesYouProfiles
    }

    fun getAllLikedByUsersCount(fromUpgradeActivity: Boolean = false, callback: (users: ArrayList<String>) -> Unit) {
        if (UserObject.allLikesYouProfiles.isEmpty() || fromUpgradeActivity) {
            val mainUserId = AccountPreferences.getInstance(this).getStringValue(Constants.firebaseUserId, "")
            val firebaseDatabaseUtil = FirebaseDatabaseUtil(this)
            firebaseDatabaseUtil.getAllLikedByUsersList(object : FirebaseGetAllLikedByUsersListListener {
                override fun onSuccess(usersList: ArrayList<String>) {
                    callback(usersList)
                    checkForLikesYouBadgeStatus(usersList)
                }
                override fun onFailure() {
                    callback(arrayListOf())
                }
            }, mainUserId)
        } else {
            checkForLikesYouBadgeStatus(UserObject.allLikesYouProfiles)
        }
    }

    private fun checkForLikesYouBadgeStatus(usersList: ArrayList<String>) {
        val likesSeenMap = AccountPreferences.getInstance(this).getStringSetValue(Constants.likesSeenMap, mutableSetOf())
        if (UserObject.hasPlusOrSelect) {
            if (usersList.isNotEmpty()) {
                usersList.map { uid ->
                    if (!likesSeenMap.contains(uid)) {
                        unseenLikesYouProfiles = true
                        setLikesYouBadge()
                    }
                }
            }
        } else {
            unseenLikesYouProfiles = usersList.isNotEmpty()
            setLikesYouBadge()
        }
        UserObject.allLikesYouProfiles = usersList
    }
}
