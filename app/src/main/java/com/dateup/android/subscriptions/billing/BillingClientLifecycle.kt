package com.dateup.android.subscriptions.billing

import android.annotation.SuppressLint
import android.app.Activity
import android.app.Application
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleObserver
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.OnLifecycleEvent
import com.android.billingclient.api.*
import com.android.billingclient.api.Purchase.PurchaseState.PURCHASED
import androidx.lifecycle.LiveData
import com.dateup.android.models.UserObject
import com.dateup.android.subscriptions.SubscriptionConstants
import com.dateup.android.subscriptions.model.PurchaseAcknowledgmentStatus
import com.dateup.android.attribution.AttributionAnalytics
import com.dateup.android.subscriptions.views.SingleLiveEvent
import timber.log.Timber

@SuppressLint("BinaryOperationInTimber")
class BillingClientLifecycle private constructor(private val app: Application) :
    LifecycleObserver, PurchasesUpdatedListener, BillingClientStateListener {

    /**
     * The purchase event is observable. Only one oberver will be notified.
     */
    val purchaseUpdateEvent = SingleLiveEvent<List<Purchase>>()

    /**
     * Purchases are observable. This list will be updated when the Billing Library
     * detects new or existing purchases. All observers will be notified.
     */
    val purchases = MutableLiveData<List<Purchase>>()

    // Deprecated in Billing v5+. Keeping for backward reference; not populated anymore.

    /**
     * ProductDetails for all known products.
     */
    private val _productsWithProductDetails = MutableLiveData<Map<String, ProductDetails>>()
    val productsWithProductDetails: LiveData<Map<String, ProductDetails>> = _productsWithProductDetails

    /**
     * Instantiate a new BillingClient instance.
     */
    lateinit var billingClient: BillingClient

    companion object {
        private const val TAG = "BillingLifecycle"

        private val LIST_OF_SKUS = listOf(
                SubscriptionConstants.MONTHLY_SKU,
                SubscriptionConstants.MONTHLY_SKU_WITH_TRAIL_OFFER,
                SubscriptionConstants.INFLUENCER_MONTHLY_SKU_WITH_TRAIL_OFFER,
                SubscriptionConstants.THREE_MONTHS_SKU,
                SubscriptionConstants.HALF_YEARLY_SKU
                )

        private val LIST_OF_PRODUCTS = listOf(
                SubscriptionConstants.SELECT_PRODUCT_ID
                )

        @Volatile
        private var INSTANCE: BillingClientLifecycle? = null

        fun getInstance(app: Application): BillingClientLifecycle =
                INSTANCE ?: synchronized(this) {
                    INSTANCE ?: BillingClientLifecycle(app).also { INSTANCE = it }
                }
    }

    @OnLifecycleEvent(Lifecycle.Event.ON_CREATE)
    fun create() {
        Timber.tag(TAG).d("ON_CREATE")
        // Create a new BillingClient in onCreate().
        // Since the BillingClient can only be used once, we need to create a new instance
        // after ending the previous connection to the Google Play Store in onDestroy().
        billingClient = BillingClient.newBuilder(app.applicationContext)
                .setListener(this)
                .enablePendingPurchases(
                    PendingPurchasesParams.newBuilder()
                        .enableOneTimeProducts()
                        .build()
                ) // Not used for subscriptions, but required by BillingClient
                .build()
        if (!billingClient.isReady) {
            Timber.tag(TAG).d("BillingClient: Start connection...")
            billingClient.startConnection(this)
        }
    }

    @OnLifecycleEvent(Lifecycle.Event.ON_DESTROY)
    fun destroy() {
        Timber.tag(TAG).d("ON_DESTROY")
        if (billingClient.isReady) {
            Timber.tag(TAG).d("BillingClient can only be used once -- closing connection")
            // BillingClient can only be used once.
            // After calling endConnection(), we must create a new BillingClient.
            billingClient.endConnection()
        }
    }

    override fun onBillingSetupFinished(billingResult: BillingResult) {
        val responseCode = billingResult.responseCode
        val debugMessage = billingResult.debugMessage
        Timber.tag(TAG).d("onBillingSetupFinished: responseCode=$responseCode, debugMessage='$debugMessage'")

        if (responseCode == BillingClient.BillingResponseCode.OK) {
            Timber.tag(TAG).i("Billing client setup successful, querying products...")
            // The billing client is ready. You can query purchases here.
            querySkuDetails()
            queryProductDetails()
            queryPurchases()
        } else {
            Timber.tag(TAG).e("Billing client setup failed: $responseCode - $debugMessage")
        }
    }

    override fun onBillingServiceDisconnected() {
        Timber.tag(TAG).d("onBillingServiceDisconnected")
        // TODO: Try connecting again with exponential backoff.
        // billingClient.startConnection(this)
    }

    // Deprecated in Billing v5+. Kept for reference to older SKU model; replaced by ProductDetails.
    private fun querySkuDetails() {
        Timber.tag(TAG).d("querySkuDetails - Skipping; using ProductDetails instead")
    }

    /**
     * Query Google Play Billing for product details.
     *
     * This uses the new Google Play Billing Library 5.0+ API for querying product details.
     */
    private fun queryProductDetails() {
        Timber.tag(TAG).d("queryProductDetails - Starting query")

        if (!billingClient.isReady) {
            Timber.tag(TAG).e("queryProductDetails: BillingClient is not ready")
            return
        }

        val productList = ArrayList<QueryProductDetailsParams.Product>()

        // Add Plus subscription SKUs (individual SKUs)
        Timber.tag(TAG).d("Adding Plus subscription SKUs: $LIST_OF_SKUS")
        LIST_OF_SKUS.forEach { sku ->
            Timber.tag(TAG).d("Adding Plus SKU: $sku")
            productList.add(
                QueryProductDetailsParams.Product.newBuilder()
                    .setProductId(sku)
                    .setProductType(BillingClient.ProductType.SUBS)
                    .build()
            )
        }

        // Add Select subscription products (product with multiple offers)
        Timber.tag(TAG).d("Adding Select subscription products: $LIST_OF_PRODUCTS")
        LIST_OF_PRODUCTS.forEach { productId ->
            Timber.tag(TAG).d("Adding Select product: $productId")
            productList.add(
                QueryProductDetailsParams.Product.newBuilder()
                    .setProductId(productId)
                    .setProductType(BillingClient.ProductType.SUBS)
                    .build()
            )
        }

        Timber.tag(TAG).d("Total products to query: ${productList.size}")

        val params = QueryProductDetailsParams.newBuilder()
            .setProductList(productList)
            .build()

        Timber.tag(TAG).d("Calling queryProductDetailsAsync...")
        billingClient.queryProductDetailsAsync(params) { billingResult, queryResult ->
            onProductDetailsResponseCompat(billingResult, queryResult.productDetailsList)
        }
    }

    /**
     * Receives the result from [queryProductDetails].
     *
     * Store the ProductDetails and post them in the [productsWithProductDetails]. This allows other parts
     * of the app to use the [ProductDetails] to show product information and make purchases.
     */
    private fun onProductDetailsResponseCompat(
        billingResult: BillingResult,
        productDetailsList: MutableList<ProductDetails>
    ) {
        val responseCode = billingResult.responseCode
        val debugMessage = billingResult.debugMessage
        Timber.tag(TAG).i("onProductDetailsResponse: responseCode=$responseCode, debugMessage='$debugMessage', productCount=${productDetailsList.size}")

        when (responseCode) {
            BillingClient.BillingResponseCode.OK -> {
                val expectedProductDetailsCount = LIST_OF_SKUS.size + LIST_OF_PRODUCTS.size
                Timber.tag(TAG).i("Expected $expectedProductDetailsCount products, received ${productDetailsList.size}")

                if (productDetailsList.isEmpty()) {
                    _productsWithProductDetails.postValue(emptyMap())
                    Timber.tag(TAG).e("onProductDetailsResponse: " +
                            "Expected ${expectedProductDetailsCount}, " +
                            "Found empty ProductDetails list. " +
                            "Requested SKUs: $LIST_OF_SKUS, " +
                            "Requested Products: $LIST_OF_PRODUCTS. " +
                            "Check to see if the products you requested are correctly published " +
                            "in the Google Play Console and match exactly (case-sensitive).")
                } else {
                    val productMap = HashMap<String, ProductDetails>()
                    for (details in productDetailsList) {
                        Timber.tag(TAG).d("Found product: ${details.productId}, name: ${details.name}, type: ${details.productType}")

                        // Log subscription offer details for SELECT products
                        if (details.productId == SubscriptionConstants.SELECT_PRODUCT_ID) {
                            details.subscriptionOfferDetails?.forEach { offer ->
                                Timber.tag(TAG).d("SELECT offer - basePlanId: ${offer.basePlanId}, offerId: ${offer.offerId}")
                            }
                        }

                        productMap[details.productId] = details
                    }
                    _productsWithProductDetails.postValue(productMap)
                    Timber.tag(TAG).i("Successfully loaded ${productMap.size} products")
                }
            }
            BillingClient.BillingResponseCode.SERVICE_DISCONNECTED -> {
                Timber.tag(TAG).e("onProductDetailsResponse: SERVICE_DISCONNECTED - $debugMessage")
            }
            BillingClient.BillingResponseCode.SERVICE_UNAVAILABLE -> {
                Timber.tag(TAG).e("onProductDetailsResponse: SERVICE_UNAVAILABLE - $debugMessage")
            }
            BillingClient.BillingResponseCode.BILLING_UNAVAILABLE -> {
                Timber.tag(TAG).e("onProductDetailsResponse: BILLING_UNAVAILABLE - $debugMessage")
            }
            BillingClient.BillingResponseCode.ITEM_UNAVAILABLE -> {
                Timber.tag(TAG).e("onProductDetailsResponse: ITEM_UNAVAILABLE - $debugMessage. " +
                        "This usually means the product IDs don't exist in Play Console or aren't published.")
            }
            BillingClient.BillingResponseCode.DEVELOPER_ERROR -> {
                Timber.tag(TAG).e("onProductDetailsResponse: DEVELOPER_ERROR - $debugMessage. " +
                        "Check app signature, package name, and product configuration.")
            }
            BillingClient.BillingResponseCode.ERROR -> {
                Timber.tag(TAG).e("onProductDetailsResponse: ERROR - $debugMessage")
            }
            BillingClient.BillingResponseCode.USER_CANCELED,
            BillingClient.BillingResponseCode.FEATURE_NOT_SUPPORTED,
            BillingClient.BillingResponseCode.ITEM_NOT_OWNED -> {
                Timber.tag(TAG).d("onProductDetailsResponse: $responseCode $debugMessage")
            }
            BillingClient.BillingResponseCode.ITEM_ALREADY_OWNED -> {
                Timber.tag(TAG).d("onProductDetailsResponse: $responseCode $debugMessage")
            }
            else -> {
                Timber.tag(TAG).e("onProductDetailsResponse: Unknown response code $responseCode - $debugMessage")
            }
        }
    }


    // Deprecated callback for SkuDetails. No-op in Billing v5+ migration.
    private fun onSkuDetailsResponseLegacy(
        billingResult: BillingResult,
        skuDetailsList: MutableList<SkuDetails>?
    ) {
        Timber.tag(TAG).d("onSkuDetailsResponseLegacy called; ignoring in favor of ProductDetails")
    }

    /**
     * Query Google Play Billing for existing purchases.
     *
     * New purchases will be provided to the PurchasesUpdatedListener.
     * You still need to check the Google Play Billing API to know when purchase tokens are removed.
     */
    private fun queryPurchases() {
        if (!billingClient.isReady) {
            Timber.tag(TAG).e("queryPurchases: BillingClient is not ready")
        }
        Timber.tag(TAG).d("queryPurchases: SUBS")

        val params = QueryPurchasesParams.newBuilder()
            .setProductType(BillingClient.ProductType.SUBS)
            .build()
        billingClient.queryPurchasesAsync(params) { billingResult, purchasesList ->
            processPurchases(purchasesList)
        }
    }

    /**
     * Launching the billing flow.
     *
     * Launching the UI to make a purchase requires a reference to the Activity.
     */
    fun launchBillingFlow(activity: Activity, params: BillingFlowParams): Int {
        if (!billingClient.isReady) {
            Timber.tag(TAG).e("launchBillingFlow: BillingClient is not ready")
        }
        val billingResult = billingClient.launchBillingFlow(activity, params)
        val responseCode = billingResult.responseCode
        val debugMessage = billingResult.debugMessage
        Timber.tag(TAG).d("launchBillingFlow: BillingResponse $responseCode $debugMessage")
        return responseCode
    }

    /**
     * Called by the Billing Library when new purchases are detected.
     */
    override fun onPurchasesUpdated(
            billingResult: BillingResult,
            purchases: MutableList<Purchase>?) {
        val responseCode = billingResult.responseCode
        val debugMessage = billingResult.debugMessage
        Timber.tag(TAG).d("onPurchasesUpdated: $responseCode $debugMessage")
        when (responseCode) {
            BillingClient.BillingResponseCode.OK -> {
                if (purchases == null) {
                    Timber.tag(TAG).d("onPurchasesUpdated: null purchase list")
                    processPurchases(null)
                } else {
                    processPurchases(purchases)
                    purchases?.let {
                        purchaseUpdateEvent.postValue(it)
                    }
                }
            }
            BillingClient.BillingResponseCode.USER_CANCELED -> {
                Timber.tag(TAG).i("onPurchasesUpdated: User canceled the purchase")
                AttributionAnalytics.logSubscriptionFailure(
                    app.applicationContext,
                    responseCode,
                    "User canceled the purchase"
                )
            }
            BillingClient.BillingResponseCode.ITEM_ALREADY_OWNED -> {
                Timber.tag(TAG).i("onPurchasesUpdated: The user already owns this item")
                AttributionAnalytics.logSubscriptionFailure(
                    app.applicationContext,
                    responseCode,
                    "Item already owned"
                )
            }
            BillingClient.BillingResponseCode.DEVELOPER_ERROR -> {
                val errorMessage = "Developer error means that Google Play " +
                        "does not recognize the configuration. If you are just getting started, " +
                        "make sure you have configured the application correctly in the " +
                        "Google Play Console. The SKU product ID must match and the APK you " +
                        "are using must be signed with release keys."
                Timber.tag(TAG).e("onPurchasesUpdated: $errorMessage")
                AttributionAnalytics.logSubscriptionFailure(
                    app.applicationContext,
                    responseCode,
                    errorMessage
                )
            }
            else -> {
                // Handle any other error codes
                Timber.tag(TAG).e("onPurchasesUpdated: Error $responseCode - $debugMessage")
                AttributionAnalytics.logSubscriptionFailure(
                    app.applicationContext,
                    responseCode,
                    debugMessage ?: "Unknown error"
                )
            }
        }
    }

    /**
     * Send purchase SingleLiveEvent and update purchases LiveData.
     *
     * The SingleLiveEvent will trigger network call to verify the subscriptions on the sever.
     * The LiveData will allow Google Play settings UI to update based on the latest purchase data.
     */
    private fun processPurchases(purchasesList: List<Purchase>?) {
        Timber.tag(TAG).d("processPurchases: ${purchasesList?.size} purchase(s)")

        if (!purchasesList.isNullOrEmpty()) {
            val hasActivePlusSubscription = purchasesList.any { purchase ->
                purchase.skus.any { sku ->
                    sku in listOf(
                        SubscriptionConstants.MONTHLY_SKU,
                        SubscriptionConstants.THREE_MONTHS_SKU,
                        SubscriptionConstants.HALF_YEARLY_SKU,
                        SubscriptionConstants.MONTHLY_SKU_WITH_TRAIL_OFFER,
                        SubscriptionConstants.INFLUENCER_MONTHLY_SKU_WITH_TRAIL_OFFER
                    )
                }
            }

            val hasActiveSelectSubscription = purchasesList.any { purchase ->
                purchase.skus.any { sku ->
                    sku == SubscriptionConstants.SELECT_PRODUCT_ID
                }
            }

            // Update subscription status in UserObject
            UserObject.isDateUpPlusUser = hasActivePlusSubscription
            UserObject.isDateUpSelectUser = hasActiveSelectSubscription
        } else {
            UserObject.isDateUpPlusUser = false
            UserObject.isDateUpSelectUser = false
            // There is a possibility that the user purchased dateup plus/select from iOS
            // and switched to android
            // check the database to determine if the user is dateup plus/select user.
            // Should get the dateupPlus/userId node and see if the user status is active and not expired
        }

        purchases.postValue(purchasesList!!)
    }

    /**
     * Check whether the purchases have changed before posting changes.
     */
    private fun isUnchangedPurchaseList(purchasesList: List<Purchase>?): Boolean {
        // TODO: Optimize to avoid updates with identical data.
        return false
    }

    /**
     * Log the number of purchases that are acknowledge and not acknowledged.
     *
     * https://developer.android.com/google/play/billing/billing_library_releases_notes#2_0_acknowledge
     *
     * When the purchase is first received, it will not be acknowledge.
     * This application sends the purchase token to the server for registration. After the
     * purchase token is registered to an account, the Android app acknowledges the purchase token.
     * The next time the purchase list is updated, it will contain acknowledged purchases.
     */
    private fun logAcknowledgementStatus(purchasesList: List<Purchase>) {
        var ack_yes = 0
        var ack_no = 0
        for (purchase in purchasesList) {
            if (purchase.isAcknowledged) {
                ack_yes++
            } else {
                ack_no++
            }
        }
        Timber.tag(TAG).d("logAcknowledgementStatus: acknowledged=$ack_yes unacknowledged=$ack_no")
    }

    /**
     * Acknowledge a purchase.
     *
     * https://developer.android.com/google/play/billing/billing_library_releases_notes#2_0_acknowledge
     *
     * Apps should acknowledge the purchase after confirming that the purchase token
     * has been associated with a user. This app only acknowledges purchases after
     * successfully receiving the subscription data back from the server.
     *
     * Developers can choose to acknowledge purchases from a server using the
     * Google Play Developer API. The server has direct access to the user database,
     * so using the Google Play Developer API for acknowledgement might be more reliable.
     * TODO(134506821): Acknowledge purchases on the server.
     *
     * If the purchase token is not acknowledged within 3 days,
     * then Google Play will automatically refund and revoke the purchase.
     * This behavior helps ensure that users are not charged for subscriptions unless the
     * user has successfully received access to the content.
     * This eliminates a category of issues where users complain to developers
     * that they paid for something that the app is not giving to them.
     */
    fun acknowledgePurchases(purchasesList: List<Purchase>, callback: (List<PurchaseAcknowledgmentStatus>) -> Unit) {
        Timber.tag(TAG).d("acknowledgePurchase")
        val purchaseAcknowledgmentStatusList = arrayListOf<PurchaseAcknowledgmentStatus>()
        for (purchase in purchasesList) {
            if (purchase.purchaseState == PURCHASED && !purchase.isAcknowledged) {
                val params = AcknowledgePurchaseParams.newBuilder()
                        .setPurchaseToken(purchase.purchaseToken)
                        .build()
                billingClient.acknowledgePurchase(params) { billingResult ->
                    val responseCode = billingResult.responseCode
                    val debugMessage = billingResult.debugMessage
                    Timber.tag(TAG).d("acknowledgePurchase for token: $responseCode $debugMessage")
                    if (responseCode != 1 && responseCode != 2 && responseCode != -3 && responseCode != -1 && responseCode != 6) {
                        purchaseAcknowledgmentStatusList.add(PurchaseAcknowledgmentStatus(purchase.purchaseToken, true))
                    }
                }
            }
        }
        callback(purchaseAcknowledgmentStatusList)
    }
}