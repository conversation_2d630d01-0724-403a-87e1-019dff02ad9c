package com.dateup.android.paidVersion

import android.content.Context
import com.dateup.android.firebase.FirebaseDatabaseUtil
import com.dateup.android.models.TimeServer
import com.dateup.android.models.UserObject
import com.dateup.android.utils.Utils.Companion.extractDateStringFromDateObject
import com.dateup.android.utils.Utils.Companion.getDateFromTimestamp
import com.dateup.android.utils.Utils.Companion.getDeviceDate
import com.google.firebase.functions.FirebaseFunctions
import com.google.gson.Gson
import timber.log.Timber

object NumLikesRestriction {

    var deviceLikeCounter = 0
    var deviceDateBasedOnServer: String? = null
    var likesThreshold = 10

    var tempLikeCounterInMemory = 0

    fun setDeviceLikeCounterAndDate(context: Context, callback: () -> Unit) {
        try {
            if (!UserObject.hasPlusOrSelect) {
                FirebaseFunctions.getInstance()
                        .getHttpsCallable("getUtcTimeStamp")
                        .call()
                        .addOnCompleteListener { task ->
                            if (task.isSuccessful) {
                                val result = Gson().fromJson(task.result?.getData().toString(), TimeServer::class.java)
                                if (result.timeStamp != null) {
                                    Timber.d("Testing time: ${result.timeStamp}")
                                    val dateObject = getDateFromTimestamp(result.timeStamp)
                                    deviceDateBasedOnServer = extractDateStringFromDateObject(dateObject)
                                    setLikesCounter(context, callback)
                                } else {
                                    setDateFromDevice(callback)
                                }
                            }else {
                                setDateFromDevice(callback)
                            }
                        }
            }else {
                callback()
            }
        } catch (exception: Exception) {
            Timber.e("Exception in getting time from time server: $exception")
            setDateFromDevice(callback)
        }
    }

    fun updateLikeCounterOnDeviceAndServer(context: Context) {
        val firebaseDatabaseUtil = FirebaseDatabaseUtil(context)
        deviceDateBasedOnServer?.let {
            firebaseDatabaseUtil.writeLikeCounter(it, deviceLikeCounter)
        }
    }

    private fun setDateFromDevice(callback: () -> Unit) {
        deviceDateBasedOnServer = getDeviceDate()
        callback()
    }

    private fun setLikesCounter(context: Context, callback: () -> Unit) {
        val firebaseDatabaseUtil = FirebaseDatabaseUtil(context)
        deviceDateBasedOnServer?.let { date ->
            firebaseDatabaseUtil.readLikeCounter(date) { count ->
                deviceLikeCounter = count
                callback()
            }
        }
    }
}
