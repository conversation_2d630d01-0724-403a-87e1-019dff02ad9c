package com.dateup.android.activity

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.appcompat.app.AlertDialog
import com.dateup.android.ui.BaseEdgeToEdgeActivity
import com.dateup.android.AccountPreferences
import com.dateup.android.AuthService
import com.dateup.android.R
import com.dateup.android.ScreenRouter
import com.dateup.android.activity.Intro1Activity.Companion.IS_USER_REPORTED
import com.dateup.android.activity.Intro1Activity.Companion.REPORTED_USER_MESSAGE
import com.dateup.android.activity.Intro1Activity.Companion.REPORTED_USER_TITLE
import com.dateup.android.activity.Intro1Activity.Companion.SHOULD_DELETE_USER
import com.dateup.android.deepLinking.AppInvites
import com.dateup.android.deepLinking.PromotionsHandler.handleBranchCampaignSignups
import com.dateup.android.extensions.launchModeWithNoBackStack
import com.dateup.android.firebase.FirebaseDatabaseUtil
import com.dateup.android.firebase.FirebaseRetrieveSingleUserRawDataListenerInterfaceFirestore
import com.dateup.android.firebase.RemoteConfigurationService
import com.dateup.android.models.User
import com.dateup.android.ui.browseProfiles.BrowseProfilesActivity
import com.dateup.android.utils.AppUpdateHelperUtility
import com.dateup.android.utils.AppUtils
import com.dateup.android.utils.Constants
import com.dateup.android.utils.Utils.Companion.isValidUser
import com.google.firebase.database.DataSnapshot
import io.branch.referral.Branch
import io.branch.referral.BranchError
import io.branch.referral.validators.IntegrationValidator
import org.json.JSONObject
import timber.log.Timber
import com.google.firebase.firestore.DocumentSnapshot
import java.util.*
import kotlin.concurrent.timerTask

class SplashActivity : BaseEdgeToEdgeActivity() {

    private val splashDisplayLength: Long = 500
    private val mContext: Context = this
    private lateinit var mActivity: Activity
    private lateinit var appUpdateHelperUtility: AppUpdateHelperUtility

    override fun onStart() {
        super.onStart()

        initBranch()
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_splash)

        mActivity = this
        appUpdateHelperUtility = AppUpdateHelperUtility(this)

        RemoteConfigurationService.fetchConfig(RemoteConfigurationService.APP_FORCE_UPDATE_VERSION) { upgradeVersion ->
            if (!upgradeVersion.isNullOrEmpty() &&
                appUpdateHelperUtility.isImmediateUpdateRequired(upgradeVersion, application)) {

                appUpdateHelperUtility.checkIfAppUpdateAvailable { isAppUpdateAvailable ->

                    if (isAppUpdateAvailable) {

                        appUpdateHelperUtility.startImmediateUpdate()
                    } else {

                        showNextSteps()
                    }
                }
            } else {

                showNextSteps()
            }
        }
    }

    override fun onResume() {
        super.onResume()
        // check If an in-app update is already in progress, if yes then resume the update.
        appUpdateHelperUtility.startImmediateUpdateIfInProgress()
    }

    private fun showNextSteps() {
        val signUpStatus = AccountPreferences.getInstance(this).getBooleanValue(Constants.isOnboardingComplete, false)

        val splashTimer = Timer("SplashTimer")

        when {
            signUpStatus -> {

                splashTimer.schedule(timerTask {

                    checkOnboardingStatus()
                }, splashDisplayLength)
            }
            else -> {
                splashTimer.schedule(timerTask {

                    handleInitialRoute()
                }, splashDisplayLength)
            }
        }

        AppInvites.handleDynamicLinks(intent, this)
    }

    private fun checkOnboardingStatus() {

        if (!AppUtils.isNetworkConnected(application)) {

            ScreenRouter.navigateToBlankNoNetworkConnectionScreen(this)
        }

        val firebaseDatabaseReference = FirebaseDatabaseUtil(this)
        firebaseDatabaseReference.readMainUserMapRawDataFromFirebaseFirestore(object :
            FirebaseRetrieveSingleUserRawDataListenerInterfaceFirestore {
            override fun onSuccess(userDocumentSnapShot: DocumentSnapshot) {

                if (userDocumentSnapShot.contains(Constants.isOnboardingComplete)) {

                    val user = userDocumentSnapShot.toObject(User::class.java)

                    val isOnboardinComplete: Boolean? = user?.isOnboardingComplete
                    val isUserReported: Boolean? = user?.isUserReported
                    val shouldDelete = user?.rsDel?: false

                    if (isUserReported == true) {

                        val reportedUserTitle: String? = user.rTitle as? String
                        val reportedUserMessage: String? = user.rMsg as? String

                        val intent = Intro1Activity.newIntent(this@SplashActivity).launchModeWithNoBackStack()
                        intent.putExtra(IS_USER_REPORTED, true)
                        intent.putExtra(SHOULD_DELETE_USER, shouldDelete)
                        intent.putExtra(REPORTED_USER_TITLE, reportedUserTitle)
                        intent.putExtra(REPORTED_USER_MESSAGE, reportedUserMessage)
                        startActivity(intent)
                    } else if (isOnboardinComplete == true && isValidUser(user)) {

                        showHomeActivity()

                    } else {

                        showIntroActivity()
                    }
                } else {

                    handleInitialRoute()
                }
            }

            override fun onFailure() {

                showIntroActivity()
            }
        })
    }

    fun showHomeActivity() {

        val intent = Intent(mContext, BrowseProfilesActivity::class.java).launchModeWithNoBackStack()
        mContext.startActivity(intent)

        FirebaseDatabaseUtil(this).saveLastSeen()
    }

    fun handleInitialRoute() {

        val lastSeenScreen = ScreenRouter.getLastScreenScreen(mContext)

        if ("" != lastSeenScreen) {

            mActivity.runOnUiThread {

                showAlertDialogForLastSeenScreen(lastSeenScreen)
            }
        } else {

            showIntroActivity()
        }
    }

    private fun showIntroActivity() {
        val intent = Intent(this, Intro1Activity::class.java)
        finish()
        this.startActivity(intent)
    }

    private fun showAlertDialogForLastSeenScreen(lastSeenScreen: String) {

        val dialogBuilder = AlertDialog.Builder(this)

        dialogBuilder.setMessage("")
                ?.setCancelable(false)
                ?.setPositiveButton("Continue") { dialog, id ->

                    ScreenRouter.navigate(lastSeenScreen, mContext)
                }
                ?.setNegativeButton("Restart") { dialog, id ->

                    AuthService.cleanup(this)

                    val intent = Intent(mContext, Intro1Activity::class.java).launchModeWithNoBackStack()
                    mContext.startActivity(intent)
                }

        val alert = dialogBuilder.create()
        alert.setMessage("Would you like to restart the signup process or continue with your last session?")
        alert.show()
    }

    override fun onActivityResult(requestCode: Int,
                                  resultCode: Int,
                                  data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)

        if (requestCode == AppUpdateHelperUtility.REQUEST_CODE_IMMEDIATE_UPDATE) {
            appUpdateHelperUtility.handleOnActivityResult(resultCode)
        }
    }

    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        this.intent = intent

        // if activity is in foreground (or in backstack but partially visible) launch the same
        // activity will skip onStart, handle this case with reInit
        if (intent.hasExtra("branch_force_new_session") &&
            intent.getBooleanExtra("branch_force_new_session", false)) {
            Branch.sessionBuilder(this).withCallback{ referringParams, error ->
                if (error == null) {
                    // Retrieve deeplink keys from 'referringParams' and evaluate the values to determine where to route the user
                    // Check '+clicked_branch_link' before deciding whether to use your Branch routing logic
                    val source = referringParams?.optString(Constants.source)
                    if (!source.isNullOrEmpty()) {
                        Timber.d("Testing branch source: $source")
                        handleBranchCampaignSignups(this@SplashActivity, source)
                    }
                } else {
                    Timber.e("BRANCH SDK Error ${error.message}")
                }
            }.reInit()
        }
    }

    private fun initBranch() {
        // Branch init
        Branch.sessionBuilder(this).withCallback { referringParams, error ->
            if (error == null) {
                // Retrieve deeplink keys from 'referringParams' and evaluate the values to determine where to route the user
                // Check '+clicked_branch_link' before deciding whether to use your Branch routing logic
                val source = referringParams?.optString(Constants.source)
                if (!source.isNullOrEmpty()) {
                    Timber.d("Testing branch source: $source")
                    handleBranchCampaignSignups(this@SplashActivity, source)
                }
            } else {
                Timber.e("BRANCH SDK Error ${error.message}")
            }
        }.withData(this.intent?.data).init()
    }

    companion object {

        fun newIntent(context: Context): Intent {

            return Intent(context, SplashActivity::class.java)
        }
    }
}
