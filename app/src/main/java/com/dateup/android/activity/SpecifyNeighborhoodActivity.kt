package com.dateup.android.activity

import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.location.*
import android.os.Bundle
import android.text.SpannableString
import android.widget.Button
import android.widget.TextView
import com.dateup.android.ui.BaseEdgeToEdgeActivity
import androidx.core.app.ActivityCompat
import com.dateup.android.AccountPreferences
import com.dateup.android.R
import com.dateup.android.ScreenRouter
import com.dateup.android.firebase.FirebaseDatabaseUtil
import com.dateup.android.utils.Constants
import java.util.*

class SpecifyNeighborhoodActivity : BaseEdgeToEdgeActivity(), LocationListener {

    companion object {

        fun newIntent(context: Context): Intent {

            // Fill the created intent with the data you want to be passed to this Activity when it's opened.
            return Intent(context, SpecifyNeighborhoodActivity::class.java)
        }
    }

    private lateinit var buttonLargeActiveButton: Button
    private lateinit var pinchAndDragTheMtextView: TextView
    private lateinit var onlyNeighborhoodNtextView: TextView
    private lateinit var setYourNeighborhooTextView: TextView
    private lateinit var locationLabelTextView: TextView

    private lateinit var locationManager: LocationManager

    override fun onCreate(savedInstanceState: Bundle?) {

        super.onCreate(savedInstanceState)
        this.setContentView(R.layout.specify_neighborhood_activity)
        this.init()

        ScreenRouter.saveScreenInfoToFirebase(this, this::class.java.simpleName)
    }

    private fun init() {

        locationManager = getSystemService(Context.LOCATION_SERVICE) as LocationManager
        if (ActivityCompat.checkSelfPermission(this, android.Manifest.permission.ACCESS_FINE_LOCATION) != PackageManager.PERMISSION_GRANTED && ActivityCompat.checkSelfPermission(this, android.Manifest.permission.ACCESS_COARSE_LOCATION) != PackageManager.PERMISSION_GRANTED) {
            //Permission has not been granted
            //Ask user to go to settings screen and turn the location permission
        } else {
            //600000 = 10 minutes
            //5000 = 3 miles
            locationManager.requestLocationUpdates(LocationManager.GPS_PROVIDER, 600, 5000f, this)
        }

        // Configure button_large_active component
        buttonLargeActiveButton = this.findViewById(R.id.button_enable_location)
        buttonLargeActiveButton.setOnClickListener {
            this.onButtonLargeActivePressed()
        }

        // Configure Pinch and drag the m component
        pinchAndDragTheMtextView = this.findViewById(R.id.pinch_and_drag_the_mtext_view)
        val pinchAndDragTheMtextViewText = SpannableString(this.getString(R.string.specify_neighborhood_activity_pinch_and_drag_the_mtext_view_text))
        pinchAndDragTheMtextView.text = pinchAndDragTheMtextViewText

        // Configure (Only neighborhood n component
        onlyNeighborhoodNtextView = this.findViewById(R.id.only_neighborhood_ntext_view)
        val onlyNeighborhoodNtextViewText = SpannableString(this.getString(R.string.specify_neighborhood_activity_only_neighborhood_ntext_view_text))
        onlyNeighborhoodNtextView.text = onlyNeighborhoodNtextViewText

        // Configure Set Your Neighborhoo component
        setYourNeighborhooTextView = this.findViewById(R.id.set_your_neighborhoo_text_view)
        val setYourNeighborhooTextViewText = SpannableString(this.getString(R.string.specify_neighborhood_activity_set_your_neighborhoo_text_view_text))
        setYourNeighborhooTextView.text = setYourNeighborhooTextViewText

        // Configure SUN VALLEY, NV component
        locationLabelTextView = this.findViewById(R.id.sun_valley_nv_text_view)
        val sunValleyNvTextViewText = ""
        locationLabelTextView.text = sunValleyNvTextViewText
    }

    fun onButtonLargeActivePressed() {

        startFirstNameActivity()
    }

    private fun startFirstNameActivity() {

        val firebaseDatabaseReference = FirebaseDatabaseUtil(this)

        firebaseDatabaseReference.saveUserInfoToFirebase(Constants.distance, Constants.defaultDistance)

        this.startActivity(FirstNameActivity.newIntent(this))
    }

    override fun onLocationChanged(location: Location) {

        val latitude: Double = location.latitude
        val longitude: Double = location.longitude

        val mGeocoder = Geocoder(applicationContext, Locale.getDefault())
        var zipCode = ""
        var city = ""
        var state = ""
        var country = ""
        var address: Address?

        val firebaseDatabaseReference = FirebaseDatabaseUtil(this)
        firebaseDatabaseReference.saveLocationToGeofire(latitude, longitude)

        val accountPreferences = AccountPreferences.getInstance(applicationContext)
        accountPreferences.setValue(Constants.latitude, latitude)
        accountPreferences.setValue(Constants.longitude, longitude)

        val addresses = mGeocoder.getFromLocation(latitude, longitude, 1)

        if (addresses != null && addresses.size > 0) {

            for (i in addresses.indices) {
                address = addresses[i]
                if (address != null) {
                    zipCode = address.postalCode
                    city = address.locality
                    state = address.adminArea
                    country = address.countryName
                }
            }

            firebaseDatabaseReference.saveUserInfoToFirebase(Constants.city, city)
            firebaseDatabaseReference.saveUserInfoToFirebase(Constants.state, state)
            firebaseDatabaseReference.saveUserInfoToFirebase(Constants.zip, zipCode)
            firebaseDatabaseReference.saveUserInfoToFirebase(Constants.country, country)

            locationLabelTextView.text = "$city,$state"
        }
    }

    override fun onStatusChanged(provider: String?, status: Int, bundle: Bundle?) {
    }
}
