package com.dateup.android.activity

import android.annotation.SuppressLint
import android.app.PendingIntent
import android.content.Context
import android.content.DialogInterface
import android.content.Intent
import android.content.IntentSender
import android.os.Bundle
import android.text.method.LinkMovementMethod
import android.view.View
import android.widget.Button
import android.widget.ProgressBar
import android.widget.TextView
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.IntentSenderRequest
import androidx.activity.result.contract.ActivityResultContracts
import com.dateup.android.ui.BaseEdgeToEdgeActivity
import androidx.viewpager.widget.ViewPager
import com.dateup.android.AccountPreferences
import com.dateup.android.AuthService
import com.dateup.android.R
import com.dateup.android.ScreenRouter.Companion.saveScreenInfoToFirebase
import com.dateup.android.adapter.IntroViewPagerAdapter
import com.dateup.android.analytics.ANALYTICS_FAILURE
import com.dateup.android.analytics.ANALYTICS_SUCCESS
import com.dateup.android.analytics.AnalyticsTrackingService
import com.dateup.android.analytics.ERROR_REASON
import com.dateup.android.analytics.SIGN_IN_FACEBOOK
import com.dateup.android.analytics.SIGN_IN_GOOGLE
import com.dateup.android.analytics.STATUS
import com.dateup.android.extensions.launchModeWithNoBackStack
import com.dateup.android.firebase.FirebaseDatabaseUtil
import com.dateup.android.firebase.FirebaseRetrieveSingleUserRawDataListenerInterfaceFirestore
import com.dateup.android.models.User
import com.dateup.android.models.UserObject
import com.dateup.android.services.BackgroundLocationIntentService
import com.dateup.android.ui.browseProfiles.BrowseProfilesActivity
import com.dateup.android.utils.AlertDialogView
import com.dateup.android.utils.AppUtils
import com.dateup.android.utils.Constants
import com.dateup.android.utils.ManagePermissions
import com.dateup.android.utils.Utils
import com.facebook.AccessToken
import com.facebook.CallbackManager
import com.facebook.FacebookCallback
import com.facebook.FacebookException
import com.facebook.login.LoginResult
import com.facebook.login.widget.LoginButton
import com.google.android.gms.auth.api.identity.BeginSignInRequest
import com.google.android.gms.auth.api.identity.GetSignInIntentRequest
import com.google.android.gms.auth.api.identity.Identity
import com.google.android.gms.auth.api.identity.SignInClient
import com.google.android.gms.common.api.ApiException
import com.google.android.gms.common.api.CommonStatusCodes
import com.google.android.material.tabs.TabLayout
import com.google.firebase.auth.FacebookAuthProvider
import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.auth.GoogleAuthProvider
import com.google.firebase.firestore.DocumentSnapshot
import timber.log.Timber
import java.util.UUID

class Intro1Activity : BaseEdgeToEdgeActivity() {

    companion object {

        const val IS_USER_REPORTED = "IS_USER_REPORTED"
        const val SHOULD_DELETE_USER = "SHOULD_DELETE_USER"
        const val REPORTED_USER_TITLE = "REPORTED_USER_TITLE"
        const val REPORTED_USER_MESSAGE = "REPORTED_USER_MESSAGE"
        const val IS_USER_AGE_RESTRICTED = "IS_USER_AGE_RESTRICTED"
        private const val EMAIL = "email"
        private const val PUBLIC_PROFILE = "public_profile"

        fun newIntent(context: Context): Intent {

            // Fill the created intent with the data you want to be passed to this Activity when it's opened.
            return Intent(context, Intro1Activity::class.java)
        }

        fun newIntentWithClearBackStack(context: Context): Intent {

            // Fill the created intent with the data you want to be passed to this Activity when it's opened.
            val intent = Intent(context, Intro1Activity::class.java)
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK)
            return intent
        }
    }

    private lateinit var byProceedingYouAgTextView: TextView
    private lateinit var continuePhoneNoButton: Button
    private lateinit var tabLayout: TabLayout

    private lateinit var viewPager: ViewPager

    private lateinit var context: Context
    private lateinit var googleSignInButton: Button
    private lateinit var facebookSignInButton: LoginButton
    private lateinit var progressBar: ProgressBar
    var email: String? = null
    private lateinit var signInLauncher: ActivityResultLauncher<IntentSenderRequest>
    private lateinit var firebaseUserId: String
    private var userCanceledOrDeclinedOneTap = false
    private lateinit var signInClient: SignInClient
    private lateinit var facebookCallbackManager: CallbackManager

    override fun onCreate(savedInstanceState: Bundle?) {

        super.onCreate(savedInstanceState)
        this.setContentView(R.layout.intro1_activity)
        context = this
        signInLauncher = registerForActivityResult(ActivityResultContracts.StartIntentSenderForResult()) { result ->
            handleGoogleSignInResult(result.data)
        }

        this.init()

        val shouldDelete = intent?.getBooleanExtra(SHOULD_DELETE_USER, false)
        val isUserReported = intent?.getBooleanExtra(IS_USER_REPORTED, false)
        val isUserAgeRestricted: Boolean? = intent?.getBooleanExtra(IS_USER_AGE_RESTRICTED, false)
        val title = intent?.getStringExtra(REPORTED_USER_TITLE)?: getString(R.string.login_error)
        val message = intent?.getStringExtra(REPORTED_USER_MESSAGE)?: getString(R.string.reported_user_banned_message)

        checkReportedFlows(shouldDelete, isUserReported, isUserAgeRestricted, title, message)
    }

    private fun checkReportedFlows(shouldDelete: Boolean?, isUserReported: Boolean?, isUserAgeRestricted: Boolean?, title: String?, message: String?) {
        if (shouldDelete != true) {
            AuthService.cleanup(this)
        }
        when {
            isUserAgeRestricted == true -> {
                startUserAgeRestrictedFlow()
            }
            isUserReported == true -> {
                startUserReportedFlow(shouldDelete, title?: getString(R.string.login_error), message?: getString(R.string.reported_user_banned_message))
            }
        }
    }

    private fun startUserReportedFlow(shouldDelete: Boolean?, title: String, message: String) {
        if (shouldDelete == true) {
            AlertDialogView.showAlertDialog(context = this,
                title = title,
                message = message,
                buttonPositiveText = getString(R.string.okay),
                buttonNegativeText = null) { dialog, which ->
                if (which == DialogInterface.BUTTON_POSITIVE) {
                    val firebaseDatabaseUtil = FirebaseDatabaseUtil(this)
                    firebaseDatabaseUtil.deleteAccountUsingCloudFunction {
                        AuthService.cleanup(this)
                    }
                }
            }
        } else {
            AlertDialogView.showAlertDialog(context = this,
                title = title,
                message = message,
                buttonPositiveText = getString(R.string.okay),
                buttonNegativeText = null) { dialog, which ->
                if (which == DialogInterface.BUTTON_POSITIVE) {
                    dialog.cancel()
                }
            }
        }
    }

    private fun startUserAgeRestrictedFlow() {
        AlertDialogView.showAlertDialog(context = this,
            title = getString(R.string.login_error),
            message = getString(R.string.age_banned_message),
            buttonPositiveText = getString(R.string.okay),
            buttonNegativeText = null) { dialog, which ->
            if (which == DialogInterface.BUTTON_POSITIVE) {
                dialog.cancel()

            }
        }
    }

    @SuppressLint("ClickableViewAccessibility")
    private fun init() {

        progressBar = findViewById(R.id.progressBar)

        // Configure By proceeding you ag component
        byProceedingYouAgTextView = this.findViewById(R.id.by_proceeding_you_ag_text_view)

        byProceedingYouAgTextView.movementMethod = LinkMovementMethod.getInstance()

        configureSocialLogin()

        // Configure button_large_active component
        continuePhoneNoButton = this.findViewById(R.id.button_enable_location)
        continuePhoneNoButton.setOnClickListener { view ->
            this.onButtonContinuePhoneNoPressed()
        }

        viewPager = findViewById<View>(R.id.view_pager) as ViewPager
        viewPager.adapter = IntroViewPagerAdapter(this)


        tabLayout = this.findViewById(R.id.tab_layout)
        tabLayout.setupWithViewPager(viewPager)

        saveScreenInfoToFirebase(this, this::class.java.simpleName)
    }

    private fun configureSocialLogin() {
        // Configure Google Sign In
        signInClient = Identity.getSignInClient(this)
        facebookCallbackManager = CallbackManager.Factory.create()
        facebookSignInButton = findViewById(R.id.facebook_sign_in_button)
        facebookSignInButton.setPermissions(EMAIL, PUBLIC_PROFILE)
        googleSignInButton = findViewById(R.id.google_sign_in_button)

        initFacebookAuth()

        googleSignInButton.setOnClickListener {
            if (userCanceledOrDeclinedOneTap) {
                startGoogleSignInRegular()
            } else {
                oneTapSignIn()
            }
        }
    }

    private fun onButtonContinuePhoneNoPressed() {

        startPhoneNumberActivity()
    }

    private fun startPhoneNumberActivity() {

        this.startActivity(PhoneNumberActivity.newIntent(this))
    }

    private fun showLoginError() {
        hideProgress()
        AlertDialogView.showAlertDialog(context = this,
            title = getString(R.string.login_error),
            message = getString(R.string.login_error_desc_generic),
            buttonPositiveText = getString(R.string.okay),
            buttonNegativeText = null) { dialog, which ->
            if (which == DialogInterface.BUTTON_POSITIVE) {
                dialog.cancel()
            }
        }
    }

    private fun oneTapSignIn() {
        // Configure One Tap UI
        val oneTapRequest = BeginSignInRequest.builder()
            .setGoogleIdTokenRequestOptions(
                BeginSignInRequest.GoogleIdTokenRequestOptions.builder()
                    .setSupported(true)
                    .setServerClientId(getString(R.string.default_web_client_id))
                    .setFilterByAuthorizedAccounts(true)
                    .build(),
            )
            .build()

        // Display the One Tap UI
        signInClient.beginSignIn(oneTapRequest)
            .addOnSuccessListener { result ->
                launchGoogleSignIn(result.pendingIntent)
            }
            .addOnFailureListener { e ->
                Timber.e(e)
                Timber.e("Google sign in Failed: ${e.stackTrace}", e)
                userCanceledOrDeclinedOneTap = true
                startGoogleSignInRegular()
                // No saved credentials found. Launch the One Tap sign-up flow, or
                // do nothing and continue presenting the signed-out UI.
            }
    }

    private fun startGoogleSignInRegular() {
        val signInRequest = GetSignInIntentRequest.builder()
            .setServerClientId(getString(R.string.default_web_client_id))
            .build()

        signInClient.getSignInIntent(signInRequest)
            .addOnSuccessListener { pendingIntent ->
                launchGoogleSignIn(pendingIntent)
            }
            .addOnFailureListener { e ->
                Timber.e(e)
                Timber.e("Google Sign-in failed ${e.stackTrace}", e)
            }
    }

    private fun launchGoogleSignIn(pendingIntent: PendingIntent) {
        try {
            val intentSenderRequest = IntentSenderRequest.Builder(pendingIntent).build()
            signInLauncher.launch(intentSenderRequest)
        } catch (e: IntentSender.SendIntentException) {
            Timber.e(e)
            Timber.e( "Couldn't start Sign In: ${e.localizedMessage}")
            showLoginError()
        }
    }

    private fun handleGoogleSignInResult(data: Intent?) {
        showProgress()
        // Result returned from launching the Sign In PendingIntent
        try {
            // Google Sign In was successful, authenticate with Firebase
            val credential = signInClient.getSignInCredentialFromIntent(data)
            val idToken = credential.googleIdToken

            if (idToken != null) {
                Timber.d("firebaseAuthWithGoogle: ${credential.id}")
                startFirebaseAuthWithGoogle(idToken)
            } else {
                // Shouldn't happen.
                Timber.e("Google No ID token!")
            }
        } catch (e: ApiException) {
            // Google Sign In failed, update UI appropriately
            when (e.statusCode) {
                CommonStatusCodes.CANCELED -> {
                    // The user closed the dialog
                    userCanceledOrDeclinedOneTap = true
                }
                CommonStatusCodes.NETWORK_ERROR -> {
                    // No Internet connection ?
                }
                else -> {
                    Timber.e(e)
                    Timber.e( "Google sign in failed: ${e.stackTrace}", e.message)
                }
            }
        }
    }

    private fun startFirebaseAuthWithGoogle(idToken: String) {
        val credential = GoogleAuthProvider.getCredential(idToken, null)
        FirebaseAuth.getInstance().signInWithCredential(credential)
            .addOnCompleteListener(this) { task ->
                if (task.isSuccessful) {
                    // Sign in success, update UI with the signed-in user's information
                    Timber.d("signInWithCredential google:success")
                    firebaseUserId = task.result?.user?.uid ?: ""
                    val user = FirebaseAuth.getInstance().currentUser
                    email = user?.email

                    signInSuccess(SIGN_IN_GOOGLE)
                } else {
                    // If sign in fails, display a message to the user.
                    Timber.d( "signInWithCredential google:failure", task.exception)
                    signInFailure(task.exception, SIGN_IN_GOOGLE)
                    showLoginError()
                }
            }
    }

    private fun initFacebookAuth() {
        facebookSignInButton.registerCallback(
            facebookCallbackManager,
            object : FacebookCallback<LoginResult> {
                override fun onSuccess(result: LoginResult) {
                    Timber.d("facebook:signin success")
                    showProgress()
                    handleFacebookAccessToken(result.accessToken)
                }

                override fun onCancel() {
                    Timber.d( "facebook:onCancel")
                }

                override fun onError(error: FacebookException) {
                    Timber.e(error)
                    Timber.e( "facebook:onError ${error.stackTrace}", error)
                    showLoginError()
                }
            },
        )
    }

    private fun handleFacebookAccessToken(token: AccessToken) {
        val credential = FacebookAuthProvider.getCredential(token.token)
        FirebaseAuth.getInstance().signInWithCredential(credential)
            .addOnCompleteListener(this) { task ->
                if (task.isSuccessful) {
                    // Sign in success, update UI with the signed-in user's information
                    Timber.d( "signInWithCredential facebook:success")
                    firebaseUserId = task.result?.user?.uid ?: ""
                    val user = FirebaseAuth.getInstance().currentUser
                    email = user?.email
                    signInSuccess(SIGN_IN_FACEBOOK)
                } else {
                    // If sign in fails, display a message to the user.
                    Timber.e(task.exception)
                    Timber.e( "signInWithCredential facebook:failure ${task.exception?.stackTrace}", task.exception)
                    signInFailure(task.exception, SIGN_IN_FACEBOOK)
                    showLoginError()
                }
            }
    }

    private fun saveEmail() {
        if (email != null) {
            val accountPreferences = AccountPreferences.getInstance(applicationContext)
            accountPreferences.setValue(Constants.email, email)
            val firebaseDatabaseReference = FirebaseDatabaseUtil(this)
            firebaseDatabaseReference.saveUserInfoToFirebase(Constants.email, email.toString())
        }
    }

    private fun saveDeviceDetailsToFirebase() {

        val firebaseDatabaseReference = FirebaseDatabaseUtil(this)
        //saving notification settings
        firebaseDatabaseReference.saveUserInfoToFirebase(Constants.deviceOs, Constants.android)
        firebaseDatabaseReference.saveUserInfoToFirebase(Constants.showMessages, true)
        firebaseDatabaseReference.saveUserInfoToFirebase(Constants.showNewMatches, true)
        Utils.setDeviceHardwareIdentifier(this)
        setVersion()

        val deviceDetails = android.os.Build.BRAND + "-" + android.os.Build.MODEL + "-" + android.os.Build.VERSION.SDK_INT + "-" + android.os.Build.VERSION.RELEASE
        firebaseDatabaseReference.saveUserInfoToFirebase(Constants.deviceDetails, deviceDetails)

        saveEmail()
    }

    private fun signInSuccess(signInType: String) {
        AnalyticsTrackingService.setUserId(this, firebaseUserId)
        AccountPreferences.getInstance(applicationContext).setValue(Constants.firebaseUserId, firebaseUserId)

        FirebaseAuth.getInstance().currentUser?.getIdToken(false)

        checkOnboardingStatus()

        val bundle = Bundle()
        bundle.putString(STATUS, ANALYTICS_SUCCESS)
        AnalyticsTrackingService.logEvent(context, signInType, bundle)
    }

    private fun signInFailure(exception: Exception?, signInType: String) {
        val bundle = Bundle()
        bundle.putString(STATUS, ANALYTICS_FAILURE)
        exception.let {
            bundle.putString(ERROR_REASON, it?.message)
        }
        AnalyticsTrackingService.logEvent(context, signInType, bundle)
    }

    private fun checkOnboardingStatus() {

        val firebaseDatabaseReference = FirebaseDatabaseUtil(this)

        firebaseDatabaseReference.readMainUserMapRawDataFromFirebaseFirestore(object :
            FirebaseRetrieveSingleUserRawDataListenerInterfaceFirestore {
            override fun onSuccess(userDocumentSnapShot: DocumentSnapshot) {

                if (!userDocumentSnapShot.contains(Constants.createdOn)) {

                    firebaseDatabaseReference.saveTimeStamp(Constants.createdOn)
                }

                if (!userDocumentSnapShot.contains(Constants.externalId)) {
                    val externalId = UUID.randomUUID().toString()
                    firebaseDatabaseReference.saveUserInfoToFirebase(Constants.externalId, externalId)
                    AccountPreferences.getInstance(applicationContext).setValue(Constants.externalId, externalId)
                }

                val user = userDocumentSnapShot.toObject(User::class.java)
                val dob = user?.dob

                firebaseDatabaseReference.saveInfluencerSignupInFirebase(user?.freeTrailUsed?: false, this@Intro1Activity) {}

                if (dob != null && Utils.isUserAgerestricted(dob.toString())) {
                    checkReportedFlows(null, null, true, null, null)
                } else if (userDocumentSnapShot.contains(Constants.isOnboardingComplete)) {
                    val isOnboardinComplete: Boolean? = user?.isOnboardingComplete
                    val isUserReported: Boolean? = user?.isUserReported
                    val shouldDelete = user?.rsDel?: false
                    if (isUserReported == true) {
                        val reportedUserTitle: String? = user.rTitle as? String
                        val reportedUserMessage: String? = user.rMsg as? String
                        checkReportedFlows(shouldDelete, true, null, reportedUserTitle , reportedUserMessage)
                    } else if (isOnboardinComplete == true) {
                        if (Utils.isValidUser(user)) {
                            UserObject.setAccountPreferences(user, context)
                            UserObject.isServerOnboardingComplete = true
                            UserObject.userFirebaseId = firebaseUserId
                            startLocationActivity()
                        } else {
                            startDobActivity()
                        }
                    } else {
                        startDobActivity()
                    }
                } else {
                    startDobActivity()
                }
            }

            override fun onFailure() {

                startDobActivity()
            }
        })
    }

    private fun startLocationActivity() {
        saveDeviceDetailsToFirebase()

        val managePermissions = ManagePermissions(this, BackgroundLocationIntentService.locationPermissionsList, BackgroundLocationIntentService.locationPermissionRequestCode)
        if (managePermissions.isPermissionGranted()) {
            startFirstNameActivity()
            val intent = Intent(this, BackgroundLocationIntentService::class.java)
            BackgroundLocationIntentService.enqueueWork(this, intent)
        } else {
            this.startActivity(EnableLocationActivity.newIntent(this))
        }
    }

    private fun startFirstNameActivity() {
        if (UserObject.isServerOnboardingComplete) {

            val intent = BrowseProfilesActivity.newIntent(this).launchModeWithNoBackStack()
            this.startActivity(intent)
        } else {
            this.startActivity(FirstNameActivity.newIntent(this))
        }
    }

    private fun startDobActivity() {
        hideProgress()
        saveDeviceDetailsToFirebase()
        this.startActivity(DateOfBirthActivity.newIntent(this))
    }

    private fun showProgress() {
        progressBar.visibility = View.VISIBLE
    }

    private fun hideProgress() {
        progressBar.visibility = View.GONE
    }

    private fun setVersion() {
        if (!UserObject.isVersionSet) {
            val version = AppUtils.getAppVersion()
            val firebaseDatabaseReference = FirebaseDatabaseUtil(this)
            firebaseDatabaseReference.saveUserInfoToFirebase(Constants.version, version)
            UserObject.isVersionSet = true
        }
    }
}

enum class Model(val layoutResId: Int) {
    INTRO_IMAGE_1(R.layout.intro1_image),
    INTRO_IMAGE_2(R.layout.intro2_image),
    INTRO_IMAGE_3(R.layout.intro3_image),
    INTRO_IMAGE_4(R.layout.intro4_image)
}