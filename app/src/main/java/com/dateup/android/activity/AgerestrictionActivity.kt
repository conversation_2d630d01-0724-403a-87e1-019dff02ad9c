package com.dateup.android.activity

import android.content.Context
import android.content.Intent
import android.os.Bundle
import com.dateup.android.ui.BaseEdgeToEdgeActivity
import com.dateup.android.R
import com.dateup.android.ScreenRouter
import com.dateup.android.ScreenRouter.Companion.setLastSeenScreen
import com.dateup.android.databinding.AgeRestrictionActivityBinding
import com.dateup.android.utils.Utils.Companion.openGmailApp

class AgerestrictionActivity : BaseEdgeToEdgeActivity() {

    private lateinit var binding: AgeRestrictionActivityBinding

    companion object {

        const val TAG = ScreenRouter.AGE_RESTRICTION_ACTIVITY

        fun newIntent(context: Context): Intent {

            // Fill the created intent with the data you want to be passed to this Activity when it's opened.
            return Intent(context, AgerestrictionActivity::class.java)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = AgeRestrictionActivityBinding.inflate(layoutInflater)
        this.setContentView(binding.root)

        binding.buttonLargeActiveButton.setOnClickListener {
            openGmailApp(this, "")
        }

        setLastSeenScreen(TAG, this)

        ScreenRouter.saveScreenInfoToFirebase(this, this::class.java.simpleName)
    }
}
