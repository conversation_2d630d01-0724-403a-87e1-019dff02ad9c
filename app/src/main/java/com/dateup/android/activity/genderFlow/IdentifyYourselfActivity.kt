package com.dateup.android.activity.genderFlow

import android.content.Context
import android.content.Intent
import android.os.Bundle
import com.dateup.android.ui.BaseEdgeToEdgeActivity
import com.dateup.android.AccountPreferences
import com.dateup.android.R
import com.dateup.android.ScreenRouter
import com.dateup.android.analytics.AnalyticsTrackingService
import com.dateup.android.analytics.USER_GENDER_PROPERTY
import com.dateup.android.analytics.USER_ORIENTATION_PROPERTY
import com.dateup.android.databinding.ActivityIdentifyYourselfBinding
import com.dateup.android.firebase.FirebaseDatabaseUtil
import com.dateup.android.models.GenderType
import com.dateup.android.models.OtherGenderType
import com.dateup.android.utils.AppUtils
import com.dateup.android.utils.Constants

class IdentifyYourselfActivity : BaseEdgeToEdgeActivity() {

    companion object {

        const val TAG = ScreenRouter.IDENTIFY_YOURSELF_ACTIVITY

        fun newIntent(context: Context): Intent {
            return Intent(context, IdentifyYourselfActivity::class.java)
        }
    }

    private var gender: String = ""
    private var genderButtonSelected = false
    private var otherGenderType: String = ""

    private lateinit var binding: ActivityIdentifyYourselfBinding

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityIdentifyYourselfBinding.inflate(layoutInflater)
        setContentView(binding.root)

        ScreenRouter.setLastSeenScreen(TAG, this)

        init()

        ScreenRouter.saveScreenInfoToFirebase(this, IdentifyYourselfActivity::class.java.simpleName)
    }

    private fun init() {

        binding.notificationsHeaderLeftImageView.setOnClickListener {
            onBackPressed()
        }

        binding.buttonPreferenceMan.setOnClickListener {
            this.onManButtonPressed()
        }

        binding.buttonPreferenceWoman.setOnClickListener { view ->
            this.onWomanButtonPressed()
        }

        binding.buttonPreferenceNonBinary.setOnClickListener { view ->
            this.onNonBinaryButtonPressed()
        }

        binding.buttonMoreOptions.setOnClickListener { view ->
            this.onMoreOptionsPressed()
        }

        binding.buttonNext.setOnClickListener { view ->
            this.onButtonLargeActivePressed()
        }
    }

    private fun onManButtonPressed() {
        binding.buttonPreferenceMan.setBackgroundResource(R.drawable.sex_preference_activity_radio_button_selected_button_selector)
        binding.buttonPreferenceWoman.setBackgroundResource(R.drawable.sex_preference_activity_radio_button_unselected_button_selector)
        binding.buttonPreferenceNonBinary.setBackgroundResource(R.drawable.sex_preference_activity_radio_button_unselected_button_selector)
        gender = GenderType.man.toString()
        otherGenderType = ""
        genderButtonSelected = true
        enableOrDisableButton()
    }

    private fun onWomanButtonPressed() {
        binding.buttonPreferenceWoman.setBackgroundResource(R.drawable.sex_preference_activity_radio_button_selected_button_selector)
        binding.buttonPreferenceMan.setBackgroundResource(R.drawable.sex_preference_activity_radio_button_unselected_button_selector)
        binding.buttonPreferenceNonBinary.setBackgroundResource(R.drawable.sex_preference_activity_radio_button_unselected_button_selector)
        gender = GenderType.woman.toString()
        otherGenderType = ""
        genderButtonSelected = true
        enableOrDisableButton()
    }

    private fun onNonBinaryButtonPressed() {
        binding.buttonPreferenceNonBinary.setBackgroundResource(R.drawable.sex_preference_activity_radio_button_selected_button_selector)
        binding.buttonPreferenceMan.setBackgroundResource(R.drawable.sex_preference_activity_radio_button_unselected_button_selector)
        binding.buttonPreferenceWoman.setBackgroundResource(R.drawable.sex_preference_activity_radio_button_unselected_button_selector)
        gender = ""
        otherGenderType = OtherGenderType.nonbinary.toString()
        genderButtonSelected = true
        enableOrDisableButton()
    }

    private fun onMoreOptionsPressed() {
        binding.buttonPreferenceMan.setBackgroundResource(R.drawable.sex_preference_activity_radio_button_unselected_button_selector)
        binding.buttonPreferenceWoman.setBackgroundResource(R.drawable.sex_preference_activity_radio_button_unselected_button_selector)
        binding.buttonPreferenceNonBinary.setBackgroundResource(R.drawable.sex_preference_activity_radio_button_unselected_button_selector)
        gender = ""
        genderButtonSelected = false
        enableOrDisableButton()
        startGenderListActivity()
    }

    private fun enableOrDisableButton() {
        if (genderButtonSelected) {
            binding.buttonNext.isEnabled = true
            binding.buttonNext.setBackgroundResource(R.drawable.phone_number_activity_button_large_active_button_selector)
        } else {
            binding.buttonNext.isEnabled = false
            binding.buttonNext.setBackgroundResource(R.drawable.bottom_button_disabled_state)
        }
    }

    fun onButtonLargeActivePressed() {
        if (!AppUtils.isNetworkConnected(application)) {
            ScreenRouter.navigateToBlankNoNetworkConnectionScreen(this)
        } else {
            if (gender == GenderType.man.toString() || gender == GenderType.woman.toString()) {
                saveGenderPrefs()
                startInterestedInActivity()
            } else {
                if (otherGenderType.isNotEmpty()) {
                    saveGenderOrientation()
                }
                if (otherGenderType == OtherGenderType.nonbinary.toString()) {
                    startGenderListActivity()
                } else {
                    startWhoShouldSeeActivity()
                }
            }
        }
    }

    private fun saveGenderPrefs() {
        AccountPreferences.getInstance(this).setValue(Constants.gender, gender)

        val firebaseDatabaseReference = FirebaseDatabaseUtil(this)
        firebaseDatabaseReference.saveUserInfoToFirebase(Constants.gender, gender)

        AnalyticsTrackingService.setUserProperty(this, USER_GENDER_PROPERTY, gender)
    }

    private fun saveGenderOrientation() {
        AccountPreferences.getInstance(this).setValue(Constants.genderOrientation, otherGenderType)

        val firebaseDatabaseReference = FirebaseDatabaseUtil(this)
        firebaseDatabaseReference.saveUserInfoToFirebase(Constants.genderOrientation, otherGenderType)

        AnalyticsTrackingService.setUserProperty(this, USER_ORIENTATION_PROPERTY, otherGenderType)
    }

    private fun startWhoShouldSeeActivity() {
        this.startActivity(WhoShouldSeeActivity.newIntent(this))
    }

    private fun startInterestedInActivity() {
        this.startActivity(InterestedInActivity.newIntent(this))
    }

    private fun startGenderListActivity() {
        this.startActivity(GenderListActivity.newIntent(this))
    }
}