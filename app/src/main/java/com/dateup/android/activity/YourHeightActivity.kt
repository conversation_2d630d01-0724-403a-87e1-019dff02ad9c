package com.dateup.android.activity

import android.content.Context
import android.content.DialogInterface
import android.content.Intent
import android.graphics.drawable.Drawable
import android.os.Bundle
import android.text.Spannable
import android.text.SpannableString
import android.text.style.AbsoluteSizeSpan
import android.text.style.ForegroundColorSpan
import android.view.MenuItem
import android.view.View
import android.widget.Button
import android.widget.TextView
import com.dateup.android.ui.BaseEdgeToEdgeActivity
import androidx.appcompat.widget.Toolbar
import androidx.core.content.ContextCompat
import androidx.core.content.res.ResourcesCompat
import com.dateup.android.AccountPreferences
import com.dateup.android.R
import com.dateup.android.ScreenRouter
import com.dateup.android.ScreenRouter.Companion.setLastSeenScreen
import com.dateup.android.analytics.ANALYTICS_NO
import com.dateup.android.analytics.ANALYTICS_YES
import com.dateup.android.analytics.AnalyticsTrackingService
import com.dateup.android.analytics.USER_IS_A_MEMBER_PROPERTY
import com.dateup.android.firebase.FirebaseDatabaseUtil
import com.dateup.android.models.GenderType
import com.dateup.android.models.InterestedInGender
import com.dateup.android.utils.*
import com.dateup.android.utils.Utils.Companion.isAMember
import io.apptik.widget.MultiSlider


class YourHeightActivity : BaseEdgeToEdgeActivity() {

    companion object {

        const val TAG = ScreenRouter.YOUR_HEIGHT_ACTIVITY

        fun newIntent(context: Context): Intent {

            // Fill the created intent with the data you want to be passed to this Activity when it's opened.
            return Intent(context, YourHeightActivity::class.java)
        }
    }

    private lateinit var heightTextView: TextView
    private lateinit var sliderSingleCopySeekBar: MultiSlider
    private lateinit var beAsAccurateAsPoTextView: TextView
    private lateinit var buttonLargeActiveButton: Button

    private var height: Double = Constants.startingHeightInInchMen
    private lateinit var gender: String
    private lateinit var interestedIn: String

    private lateinit var toolbar: Toolbar
    private lateinit var skipTextView: TextView

    override fun onCreate(savedInstanceState: Bundle?) {

        super.onCreate(savedInstanceState)
        this.setContentView(R.layout.your_height_activity)
        this.init()

        setLastSeenScreen(TAG, this)
        ScreenRouter.saveScreenInfoToFirebase(this, this::class.java.simpleName)
    }

    private fun init() {

        setupToolbar()

        // Configure slider/single copy component
        sliderSingleCopySeekBar = this.findViewById(R.id.slider_single_copy_seek_bar)

        gender = AccountPreferences.getInstance(this).getStringValue(Constants.gender, "")
        interestedIn = AccountPreferences.getInstance(this).getStringValue(Constants.interestedIn, "")

        heightTextView = this.findViewById(R.id.height_text_view)

        if (gender == GenderType.man.toString()) {

            height = Constants.startingHeightInInchMen
            setHeightTextViewText(Constants.startingHeightInFeetForMen)
            sliderSingleCopySeekBar.getThumb(0).value = 59
        } else if (gender == GenderType.woman.toString()) {

            height = Constants.startingHeightInInchWomen
            setHeightTextViewText(Constants.startingHeightInFeetForWoMen)
            sliderSingleCopySeekBar.getThumb(0).value = 53
        }

        sliderSingleCopySeekBar.setOnThumbValueChangeListener(object : MultiSlider.SimpleChangeListener() {
            override fun onValueChanged(multiSlider: MultiSlider?, thumb: MultiSlider.Thumb?, thumbIndex: Int, value: Int) {

                val heightInches: Double = if (GenderType.woman.toString() == gender) {
                    value.times(Constants.womensHeightInInchMultiplier).plus(Constants.startingHeightInInch)
                } else {
                    value.times(Constants.mensHeightInInchMultiplier).plus(Constants.startingHeightInInch)
                }
                height = heightInches
                val heightString = Utils.heightInFeetFromInches(heightInches)
                setHeightTextViewText(heightString)
            }
        })

        // Configure Be as accurate as po component
        beAsAccurateAsPoTextView = this.findViewById(R.id.be_as_accurate_as_po_text_view)
        val beAsAccurateAsPoTextViewText = SpannableString(this.getString(R.string.your_height_activity_be_as_accurate_as_po_text_view_text))
        beAsAccurateAsPoTextViewText.setSpan(FontSpan(ResourcesCompat.getFont(this, R.font.font_nunitosans_extrabolditalic)), 28, 51, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
        beAsAccurateAsPoTextViewText.setSpan(ForegroundColorSpan(ContextCompat.getColor(this, R.color.your_height_activity_how_tall_are_you_text_view_text_color)), 28, 51, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
        beAsAccurateAsPoTextView.text = beAsAccurateAsPoTextViewText

        // Configure button_large_active component
        buttonLargeActiveButton = this.findViewById(R.id.button_enable_location)
        buttonLargeActiveButton.setOnClickListener { view ->
            this.onButtonLargeActivePressed()
        }
    }

    fun setHeightTextViewText(heightString: String) {

        val formattedHeight: String = if (heightString.length == 7) {
            heightString.substring(0, 1) + " " + heightString.substring(1, 3) + " " + heightString.substring(3, 5) + " " + heightString.substring(heightString.length - 2, heightString.length)
        } else {
            heightString.substring(0, 1) + " " + heightString.substring(1, 3) + " " + heightString.substring(3, 4) + " " + heightString.substring(heightString.length - 2, heightString.length)
        }

        val spannedHeightString = SpannableString(formattedHeight)
        spannedHeightString.setSpan(AbsoluteSizeSpan(64), 1, 4, Spannable.SPAN_INCLUSIVE_INCLUSIVE)
        spannedHeightString.setSpan(AbsoluteSizeSpan(64), formattedHeight.length - 2, formattedHeight.length, Spannable.SPAN_INCLUSIVE_INCLUSIVE)

        heightTextView.setText(spannedHeightString, TextView.BufferType.SPANNABLE)
    }

    fun onButtonLargeActivePressed() {

        if (!AppUtils.isNetworkConnected(application)) {

            ScreenRouter.navigateToBlankNoNetworkConnectionScreen(this)
        } else {

            showConfirmationDialog()
        }
    }

    private fun showConfirmationDialog() {

        AlertDialogView.showAlertDialog(context = this,
                title = getString(R.string.height_confirmation_title, Utils.heightInFeetFromInchesWithQuotes(height)),
                message = getString(R.string.height_confirmation_desc),
                buttonPositiveText = getString(R.string.height_confirmation_positive_button),
                buttonNegativeText = getString(R.string.height_confirmation_negative_button)) { dialog, which ->

            if (which == DialogInterface.BUTTON_POSITIVE) {

                AccountPreferences.getInstance(this).setValue(Constants.height, height)

                startHeightCelebrationActivity()
            } else {

                dialog.cancel()
            }
        }
    }

    fun setupToolbar() {

        toolbar = findViewById(R.id.toolbar)

        skipTextView = this.findViewById(R.id.skip_text_view)
        skipTextView.visibility = View.GONE

        setSupportActionBar(toolbar)

        val actionBar = supportActionBar
        val drawable: Drawable? = ContextCompat.getDrawable(this, R.drawable.ic_arrow_left)

        actionBar?.setHomeAsUpIndicator(drawable)
        actionBar?.setDisplayShowTitleEnabled(true)
        actionBar?.setDisplayHomeAsUpEnabled(true)
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {

        when (item.itemId) {
            android.R.id.home -> {
                onBackPressed()
                return true
            }
        }

        return super.onOptionsItemSelected(item)
    }

    private fun startHeightCelebrationActivity() {

        saveHeightToFirebase()

        if (isAMember(height, gender)) {
            this.startActivity(HeightCelebrationMembersActivity.newIntent(this))
        } else {
            this.startActivity(HeightCelebrationGuestsActivity.newIntent(this))
        }
    }

    private fun saveHeightToFirebase() {

        val firebaseDatabaseReference = FirebaseDatabaseUtil(this)

        firebaseDatabaseReference.saveUserInfoToFirebase(Constants.height, height)

        setDefaultHeights()

        if (isAMember(height, gender)) {
            AccountPreferences.getInstance(this).setValue(Constants.isAMember, true)
            firebaseDatabaseReference.saveUserInfoToFirebase(Constants.isAMember, true)
            AnalyticsTrackingService.setUserProperty(this, USER_IS_A_MEMBER_PROPERTY, ANALYTICS_YES)
        } else {
            AccountPreferences.getInstance(this).setValue(Constants.isAMember, false)
            firebaseDatabaseReference.saveUserInfoToFirebase(Constants.isAMember, false)
            AnalyticsTrackingService.setUserProperty(this, USER_IS_A_MEMBER_PROPERTY, ANALYTICS_NO)
        }
    }

    private fun setDefaultHeights() {

        val firebaseDatabaseReference = FirebaseDatabaseUtil(this)

        when (interestedIn) {
            InterestedInGender.man.toString() -> {
                //setting default match heights
                firebaseDatabaseReference.saveUserInfoToFirebase(Constants.minMatchHeight, Constants.defaultMinHeightInInchForPrefsInterestedInMen)
                firebaseDatabaseReference.saveUserInfoToFirebase(Constants.maxMatchHeight, Constants.defaultMaxHeightInInchForPrefsInterestedInMen)

                //setting default guest match heights
                firebaseDatabaseReference.saveUserInfoToFirebase(Constants.minGuestHeight, Constants.defaultMinGuestsHeightInInchForPrefsInterestedInMen)
                firebaseDatabaseReference.saveUserInfoToFirebase(Constants.maxGuestHeight, Constants.defaultMaxGuestsHeightInInchForPrefsInterestedInMen)

                AccountPreferences.getInstance(this).setValue(Constants.minMatchHeight, Constants.defaultMinHeightInInchForPrefsInterestedInMen)
                AccountPreferences.getInstance(this).setValue(Constants.maxMatchHeight, Constants.defaultMaxHeightInInchForPrefsInterestedInMen)

                //setting default guest height
                AccountPreferences.getInstance(this).setValue(Constants.minGuestHeight, Constants.defaultMinGuestsHeightInInchForPrefsInterestedInMen)
                AccountPreferences.getInstance(this).setValue(Constants.maxGuestHeight, Constants.defaultMaxGuestsHeightInInchForPrefsInterestedInMen)

            }
            InterestedInGender.woman.toString() -> {
                //setting default match heights
                firebaseDatabaseReference.saveUserInfoToFirebase(Constants.minMatchHeight, Constants.defaultMinHeightInInchForPrefsInterestedInWomen)
                firebaseDatabaseReference.saveUserInfoToFirebase(Constants.maxMatchHeight, Constants.defaultMaxHeightInInchForPrefsInterestedInWomen)

                //setting default guest match heights
                firebaseDatabaseReference.saveUserInfoToFirebase(Constants.minGuestHeight, Constants.defaultMinGuestsHeightInInchForPrefsInterestedInWomen)
                firebaseDatabaseReference.saveUserInfoToFirebase(Constants.maxGuestHeight, Constants.defaultMaxGuestsHeightInInchForPrefsInterestedInWomen)

                AccountPreferences.getInstance(this).setValue(Constants.minMatchHeight, Constants.defaultMinHeightInInchForPrefsInterestedInWomen)
                AccountPreferences.getInstance(this).setValue(Constants.maxMatchHeight, Constants.defaultMaxHeightInInchForPrefsInterestedInWomen)

                //setting default guest height
                AccountPreferences.getInstance(this).setValue(Constants.minGuestHeight, Constants.defaultMinGuestsHeightInInchForPrefsInterestedInWomen)
                AccountPreferences.getInstance(this).setValue(Constants.maxGuestHeight, Constants.defaultMaxGuestsHeightInInchForPrefsInterestedInWomen)

            }
            else -> {
                //setting default match heights
                firebaseDatabaseReference.saveUserInfoToFirebase(Constants.minMatchHeight, Constants.defaultMinHeightInInchForPrefsInterestedInMen)
                firebaseDatabaseReference.saveUserInfoToFirebase(Constants.maxMatchHeight, Constants.defaultMaxHeightInInchForPrefsInterestedInMen)

                //setting default guest match heights
                firebaseDatabaseReference.saveUserInfoToFirebase(Constants.minGuestHeight, Constants.defaultMinGuestsHeightInInchForPrefsInterestedInMen)
                firebaseDatabaseReference.saveUserInfoToFirebase(Constants.maxGuestHeight, Constants.defaultMaxGuestsHeightInInchForPrefsInterestedInMen)

                AccountPreferences.getInstance(this).setValue(Constants.minMatchHeight, Constants.defaultMinHeightInInchForPrefsInterestedInMen)
                AccountPreferences.getInstance(this).setValue(Constants.maxMatchHeight, Constants.defaultMaxHeightInInchForPrefsInterestedInMen)

                //setting default guest height
                AccountPreferences.getInstance(this).setValue(Constants.minGuestHeight, Constants.defaultMinGuestsHeightInInchForPrefsInterestedInMen)
                AccountPreferences.getInstance(this).setValue(Constants.maxGuestHeight, Constants.defaultMaxGuestsHeightInInchForPrefsInterestedInMen)
            }
        }
    }
}
