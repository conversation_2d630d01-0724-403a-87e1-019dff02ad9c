package com.dateup.android.activity

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Build
import android.os.Bundle
import android.text.Spannable
import android.text.SpannableString
import android.text.style.AbsoluteSizeSpan
import android.view.WindowInsets
import android.view.WindowManager
import android.widget.Button
import android.widget.TextView
import com.dateup.android.ui.BaseEdgeToEdgeActivity
import com.dateup.android.AccountPreferences
import com.dateup.android.R
import com.dateup.android.ScreenRouter
import com.dateup.android.ScreenRouter.Companion.setLastSeenScreen
import com.dateup.android.databinding.ActivityDateUpPledgeBinding
import com.dateup.android.databinding.AgeRestrictionActivityBinding
import com.dateup.android.databinding.HeightCelebrationActivityGuestsBinding
import com.dateup.android.extensions.launchModeWithSingleTop
import com.dateup.android.subscriptions.views.SubscriptionActivity
import com.dateup.android.models.GenderType
import com.dateup.android.utils.AppUtils
import com.dateup.android.utils.Constants
import com.dateup.android.utils.Utils
import nl.dionsegijn.konfetti.models.Shape
import nl.dionsegijn.konfetti.models.Size

class HeightCelebrationGuestsActivity : BaseEdgeToEdgeActivity() {

    private lateinit var binding: HeightCelebrationActivityGuestsBinding

    companion object {

        const val TAG = ScreenRouter.HEIGHT_CELEBRATION_GUESTS_ACTIVITY

        fun newIntent(context: Context): Intent {

            // Fill the created intent with the data you want to be passed to this Activity when it's opened.
            return Intent(context, HeightCelebrationGuestsActivity::class.java)
        }

        fun hideStatusBar(activity: Activity) {

            try {

                @Suppress("DEPRECATION")
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {

                    activity.window.insetsController?.hide(WindowInsets.Type.statusBars())
                } else {

                    activity.window.setFlags(
                            WindowManager.LayoutParams.FLAG_FULLSCREEN,
                            WindowManager.LayoutParams.FLAG_FULLSCREEN
                    )
                }
            }catch (ex: Exception) {

            }
        }
    }

    private lateinit var greatHeightTextView: TextView
    private lateinit var buttonLargeActiveButton: Button

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        hideStatusBar(this)

        binding = HeightCelebrationActivityGuestsBinding.inflate(layoutInflater)
        this.setContentView(binding.root)

        init()

        setLastSeenScreen(TAG, this)
        ScreenRouter.saveScreenInfoToFirebase(this, this::class.java.simpleName)
    }

    private fun init() {
        greatHeightTextView = this.findViewById(R.id.is_agreat_heig_text_view)

        val heightFromPrefs: Double = AccountPreferences.getInstance(this).getDoubleValue(Constants.height, 0.0)
        val height = Utils.heightInFeetFromInchesWithQuotes(heightFromPrefs)

        val px = 24 * resources.displayMetrics.scaledDensity
        val spannedHeightString = SpannableString(this.getString(R.string.height_celebration_activity_is_agreat_he_text_view_text, height))
        spannedHeightString.setSpan(AbsoluteSizeSpan(px.toInt()), 0, 5, Spannable.SPAN_INCLUSIVE_INCLUSIVE)

        greatHeightTextView.text = spannedHeightString


        // Configure button_large_active component
        buttonLargeActiveButton = this.findViewById(R.id.button_large_active_button)
        buttonLargeActiveButton.setOnClickListener { view ->
            this.onButtonLargeActivePressed()
        }

        startKonfetti()
    }

    fun onButtonLargeActivePressed() {
        navigationForGuests()
    }

    private fun navigationForGuests() {
        val gender: String = AccountPreferences.getInstance(this).getStringValue(com.dateup.android.utils.Constants.gender, "")
        val interestedIn: String = AccountPreferences.getInstance(this).getStringValue(com.dateup.android.utils.Constants.interestedIn, "")
        if (gender == GenderType.man.toString() && interestedIn == GenderType.woman.toString()) {
            startGuestHeightPreferenceActivity()
        } else {
            startProfilePicturesActivity()
        }
    }

    private fun startProfilePicturesActivity() {
        this.startActivity(Images3Activity.newIntent(this))
    }

    private fun startGuestHeightPreferenceActivity() {
        this.startActivity(GuestHeightPreferenceActivity.newIntent(this))
    }

    private fun startKonfetti() {
        binding.viewKonfetti.build()
                .addColors(resources.getColor(R.color.color_primary, null), resources.getColor(R.color.konfetti_color2, null))
                .setDirection(0.0, 359.0)
                .setSpeed(1f, 5f)
                .setFadeOutEnabled(true)
                .setTimeToLive(2000L)
                .addShapes(Shape.Square, Shape.Circle)
                .addSizes(Size(12))
                .setPosition(-50f, AppUtils.getScreenWidth(this) + 50f, -50f, -50f)
                .streamFor(300, 2000L)
    }

    @SuppressLint("MissingSuperCall")
    override fun onBackPressed() {}
}
