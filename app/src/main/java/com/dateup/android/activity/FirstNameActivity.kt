package com.dateup.android.activity

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.text.SpannableString
import android.text.TextUtils
import android.view.Menu
import android.widget.Button
import android.widget.EditText
import android.widget.TextView
import com.dateup.android.ui.BaseEdgeToEdgeActivity
import com.dateup.android.AccountPreferences
import com.dateup.android.R
import com.dateup.android.ScreenRouter
import com.dateup.android.ScreenRouter.Companion.setLastSeenScreen
import com.dateup.android.firebase.FirebaseDatabaseUtil
import com.dateup.android.utils.AppUtils
import com.dateup.android.utils.Constants
import com.dateup.android.utils.Utils.Companion.afterTextChanged

class FirstNameActivity : BaseEdgeToEdgeActivity() {

    companion object {

        const val TAG = ScreenRouter.FIRST_NAME_ACTIVITY

        fun newIntent(context: Context): Intent {

            // Fill the created intent with the data you want to be passed to this Activity when it's opened.
            return Intent(context, FirstNameActivity::class.java)
        }
    }

    private lateinit var buttonLargeActiveButton: Button
    private lateinit var thisWillBeDisplayTextView: TextView

    private lateinit var firstNameEditText: EditText

    private var firstName: String = ""

    override fun onCreate(savedInstanceState: Bundle?) {

        super.onCreate(savedInstanceState)
        this.setContentView(R.layout.first_name_activity)
        this.init()

        setLastSeenScreen(TAG, this)
        ScreenRouter.saveScreenInfoToFirebase(this, this::class.java.simpleName)
    }

    override fun onCreateOptionsMenu(menu: Menu): Boolean {

        menuInflater.inflate(R.menu.first_name_activity_menu, menu)
        return false
    }

    private fun init() {

        // Configure button_large_active component
        buttonLargeActiveButton = this.findViewById(R.id.button_enable_location)
        buttonLargeActiveButton.setOnClickListener { view ->
            this.onButtonLargeActivePressed()
        }

        // Configure This will be display component
        thisWillBeDisplayTextView = this.findViewById(R.id.this_will_be_display_text_view)
        val thisWillBeDisplayTextViewText = SpannableString(this.getString(R.string.first_name_activity_this_will_be_display_text_view_text))
        thisWillBeDisplayTextView.text = thisWillBeDisplayTextViewText

        firstNameEditText = this.findViewById(R.id.first_name_edit_text)
        firstNameEditText.requestFocus()

        firstNameEditText.afterTextChanged {
            if (it.isNotEmpty()) {
                buttonLargeActiveButton.setBackgroundResource(R.drawable.phone_number_activity_button_large_active_button_selector)
                buttonLargeActiveButton.isEnabled = true
            } else {
                buttonLargeActiveButton.setBackgroundResource(R.drawable.bottom_button_disabled_state)
                buttonLargeActiveButton.isEnabled = false
            }
        }
    }

    fun onButtonLargeActivePressed() {

        if (!AppUtils.isNetworkConnected(application)) {

            ScreenRouter.navigateToBlankNoNetworkConnectionScreen(this)
        } else {
            firstName = firstNameEditText.text.toString()

            if (!TextUtils.isEmpty(firstName)) {

                val accountPreferences = AccountPreferences.getInstance(this)
                accountPreferences.setValue(Constants.name, firstName)

                saveFirstNameToFirebase()
                startEmailActivity()
            }
        }
    }

    private fun saveFirstNameToFirebase() {

        AccountPreferences.getInstance(this).setValue(Constants.name, firstName)

        val firebaseDatabaseReference = FirebaseDatabaseUtil(this)
        firebaseDatabaseReference.saveUserInfoToFirebase(Constants.name, firstName)
    }

    private fun startEmailActivity() {
        this.startActivity(EmailActivity.newIntent(this))
    }
}
