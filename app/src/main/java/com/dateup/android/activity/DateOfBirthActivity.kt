package com.dateup.android.activity

import android.content.Context
import android.content.DialogInterface
import android.content.Intent
import android.os.Bundle
import android.text.Editable
import android.text.TextUtils
import android.text.TextWatcher
import android.view.Menu
import android.widget.Button
import android.widget.EditText
import androidx.appcompat.app.AlertDialog
import com.dateup.android.ui.BaseEdgeToEdgeActivity
import androidx.appcompat.widget.Toolbar
import com.dateup.android.AccountPreferences
import com.dateup.android.R
import com.dateup.android.ScreenRouter
import com.dateup.android.ScreenRouter.Companion.setLastSeenScreen
import com.dateup.android.analytics.AnalyticsTrackingService
import com.dateup.android.analytics.USER_AGE_PROPERTY
import com.dateup.android.firebase.FirebaseDatabaseUtil
import com.dateup.android.utils.AlertDialogView
import com.dateup.android.utils.AppUtils
import com.dateup.android.utils.Constants
import com.dateup.android.utils.Utils
import com.dateup.android.utils.Utils.Companion.isUser18YearsOld
import com.dateup.android.utils.Utils.Companion.isValidDate
import java.text.SimpleDateFormat
import java.util.*


class DateOfBirthActivity : BaseEdgeToEdgeActivity() {

    companion object {

        const val TAG = ScreenRouter.DATE_OF_BIRTH_ACTIVITY

        fun newIntent(context: Context): Intent {

            // Fill the created intent with the data you want to be passed to this Activity when it's opened.
            return Intent(context, DateOfBirthActivity::class.java)
        }
    }

    private lateinit var buttonLargeActiveButton: Button

    private lateinit var mmEditText: EditText
    private lateinit var ddEditText: EditText
    private lateinit var yyEditText: EditText

    private var dob: String = ""

    private var isEnableButton = false

    private lateinit var accountPreferences: AccountPreferences

    override fun onCreate(savedInstanceState: Bundle?) {

        super.onCreate(savedInstanceState)
        this.setContentView(R.layout.date_of_birth_activity)
        this.init()

        setLastSeenScreen(TAG, this)

        ScreenRouter.saveScreenInfoToFirebase(this, this::class.java.simpleName)
    }

    override fun onCreateOptionsMenu(menu: Menu): Boolean {

        menuInflater.inflate(R.menu.date_of_birth_activity_menu, menu)
        return false
    }

    private fun init() {

        accountPreferences = AccountPreferences.getInstance(this)
        // Configure button_large_active component
        buttonLargeActiveButton = this.findViewById(R.id.button_enable_location)
        buttonLargeActiveButton.setOnClickListener { view ->
            this.onButtonLargeActivePressed()
        }

        mmEditText = this.findViewById(R.id.mm_edit_text)
        ddEditText = this.findViewById(R.id.dd_edit_text)
        yyEditText = this.findViewById(R.id.yyyy_edit_text)

        mmEditText.requestFocus()

        var mmCounter = 0
        var ddCounter = 0

        mmEditText.afterTextChanged {
            if (!TextUtils.isEmpty(it)) {
                mmCounter += 1
                if (mmCounter == 2) {
                    ddEditText.requestFocus()
                }
            }
            setButtonState()
        }

        ddEditText.afterTextChanged {
            if (!TextUtils.isEmpty(it)) {
                ddCounter += 1
                if (ddCounter == 2) {
                    yyEditText.requestFocus()
                }
            }
            setButtonState()
        }

        yyEditText.afterTextChanged {
            if (!TextUtils.isEmpty(it) && it.length == 4) {
                ddCounter += 1
                isEnableButton = ddCounter >= 3
            }else {
                isEnableButton = false
            }
            setButtonState()
        }
    }

    private fun setButtonState() {

        if (isEnableButton) {
            buttonLargeActiveButton.setBackgroundResource(R.drawable.phone_number_activity_button_large_active_button_selector)
            buttonLargeActiveButton.isEnabled = true
        } else {
            buttonLargeActiveButton.setBackgroundResource(R.drawable.bottom_button_disabled_state)
            buttonLargeActiveButton.isEnabled = false
        }
    }

    fun onButtonLargeActivePressed() {

        if (!AppUtils.isNetworkConnected(application)) {
            ScreenRouter.navigateToBlankNoNetworkConnectionScreen(this)
        } else {
            val userBirthDate = ddEditText.text.toString()
            val userBirthMonth = mmEditText.text.toString()
            val userBirthYear = yyEditText.text.toString()

            try {

                val year = userBirthYear.toInt()

                if (year < (Calendar.getInstance().get(Calendar.YEAR) - 100)) {

                    showInvalidDateAlert()
                    return
                }
            } catch (ex: Exception) {
            }

            dob = mmEditText.text.toString() + "/" + ddEditText.text.toString() + "/" + yyEditText.text.toString()
            val userFormattedDob = yyEditText.text.toString() + "-" + mmEditText.text.toString() + "-" + ddEditText.text.toString()

            if (!isValidDate(userFormattedDob)) {

                showInvalidDateAlert()
            } else if (!TextUtils.isEmpty(userBirthDate) && !TextUtils.isEmpty(userBirthMonth) && !TextUtils.isEmpty(userBirthYear)) {

                val sdf = SimpleDateFormat("yyyy-MM-dd", Locale.US)
                val dobCalender = Calendar.getInstance()
                dobCalender.time = sdf.parse(userFormattedDob)

                accountPreferences.setValue(Constants.dob, dob)

                val dd2 = "$userBirthDate/$userBirthMonth/$userBirthYear"
                val formatter = SimpleDateFormat("dd/MM/yyyy", Locale.US)
                val dt1 = formatter.parse(dd2)
                val format2 = SimpleDateFormat("MMM", Locale.US)
                val finalMonth = format2.format(dt1)
                val userDob = "$finalMonth $userBirthDate, $userBirthYear"

                if (isUser18YearsOld(dobCalender)) {
                    showDOBConfirmationAlert(userDob)
                } else {
                    showAgeAlert(userDob)
                }
            }
        }
    }

    private fun showInvalidDateAlert() {

        AlertDialogView.showAlertDialog(context = this,
                title = "Please enter a valid date of birth.",
                message = "",
                buttonPositiveText = getString(R.string.okay),
                buttonNegativeText = null) { dialog, which ->

            if (which == DialogInterface.BUTTON_POSITIVE) {

                dialog.cancel()
            }
        }
    }

    private fun showDOBConfirmationAlert(dob: String) {
        val dialogBuilder = AlertDialog.Builder(this)

        val dialogText = getString(R.string.dob_confirmation_text, dob)
        dialogBuilder.setMessage(dialogText)
            .setCancelable(false)
            .setPositiveButton("Yes, correct") { dialog, id ->
                saveDobToFirebase()
                startWelcomeActivity()
            }
            .setNegativeButton("No") { dialog, id ->
                dialog.cancel()
            }

        val alert = dialogBuilder.create()
        alert.show()
    }

    private fun showAgeAlert(dob: String) {
        val dialogBuilder = AlertDialog.Builder(this)

        dialogBuilder.setMessage("")
                .setCancelable(false)
                .setPositiveButton("Yes") { dialog, id ->

                    startAgeRestrictionActivity()
                }
                .setNegativeButton("No") { dialog, id ->
                    dialog.cancel()
                }

        val alert = dialogBuilder.create()
        alert.setTitle("You listed your birthday as $dob. Is this correct?")
        alert.show()
    }

    private fun startAgeRestrictionActivity() {

        this.startActivity(AgerestrictionActivity.newIntent(this))
    }

    private fun saveDobToFirebase() {

        val firebaseDatabaseReference = FirebaseDatabaseUtil(this)
        firebaseDatabaseReference.saveUserInfoToFirebase(Constants.dob, dob)

        val sdf = SimpleDateFormat("MM/dd/yyyy", Locale.US)
        val date = sdf.parse(dob)
        val userAge: Int = Utils.getAge(date)

        var minMatchAge = userAge.minus(7)
        val maxMatchAge = userAge.plus(7)

        if (minMatchAge <= 18) {
            minMatchAge = 18
        }

        firebaseDatabaseReference.saveUserInfoToFirebase(Constants.minMatchAge, minMatchAge)
        firebaseDatabaseReference.saveUserInfoToFirebase(Constants.maxMatchAge, maxMatchAge)
        AccountPreferences.getInstance(this).setValue(Constants.minMatchAge, minMatchAge)
        AccountPreferences.getInstance(this).setValue(Constants.maxMatchAge, maxMatchAge)

        AnalyticsTrackingService.setUserProperty(this, USER_AGE_PROPERTY, userAge.toString())
    }

    fun EditText.afterTextChanged(afterTextChanged: (String) -> Unit) {
        this.addTextChangedListener(object : TextWatcher {
            override fun afterTextChanged(text: Editable?) {
                afterTextChanged.invoke(text.toString())
            }

            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {
            }

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
            }
        })
    }

    private fun startWelcomeActivity() {

        this.startActivity(WelcomeActivity.newIntent(this))
    }

    override fun onBackPressed() {}
}
