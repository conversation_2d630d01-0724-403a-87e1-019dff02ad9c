package com.dateup.android.activity

import android.app.Application
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.text.SpannableString
import android.widget.Button
import android.widget.TextView
import com.dateup.android.ui.BaseEdgeToEdgeActivity
import com.dateup.android.R
import com.dateup.android.ScreenRouter
import com.dateup.android.utils.AppUtils.Companion.isNetworkConnected

class BlankNoNetworkConnectionActivity : BaseEdgeToEdgeActivity() {

    companion object {

        fun newIntent(context: Context): Intent {

            // Fill the created intent with the data you want to be passed to this Activity when it's opened.
            return Intent(context, BlankNoNetworkConnectionActivity::class.java)
        }
    }

    private lateinit var noNetworkConnectioTextView: TextView
    private lateinit var pleaseMakeSureYouTextView: TextView
    private lateinit var buttonLargeActiveButton: Button

    override fun onCreate(savedInstanceState: Bundle?) {

        super.onCreate(savedInstanceState)
        this.setContentView(R.layout.blank_no_network_connection_activity)
        this.init()

        ScreenRouter.saveScreenInfoToFirebase(this, this::class.java.simpleName)
    }

    private fun init() {

        // Configure No Network Connectio component
        noNetworkConnectioTextView = this.findViewById(R.id.no_network_connectio_text_view)
        val noNetworkConnectioTextViewText = SpannableString(this.getString(R.string.blank_no_network_connection_activity_no_network_connectio_text_view_text))
        noNetworkConnectioTextView.text = noNetworkConnectioTextViewText

        // Configure Please make sure you component
        pleaseMakeSureYouTextView = this.findViewById(R.id.please_make_sure_you_text_view)
        val pleaseMakeSureYouTextViewText = SpannableString(this.getString(R.string.blank_no_network_connection_activity_please_make_sure_you_text_view_text))
        pleaseMakeSureYouTextView.text = pleaseMakeSureYouTextViewText

        // Configure button_large_active component
        buttonLargeActiveButton = this.findViewById(R.id.button_enable_location)
        buttonLargeActiveButton.setOnClickListener { view ->
            this.onButtonLargeActivePressed()
        }
    }

    fun onButtonLargeActivePressed() {
        if (isNetworkConnected(applicationContext as? Application?)) {
            onBackPressed()
        }
    }
}
