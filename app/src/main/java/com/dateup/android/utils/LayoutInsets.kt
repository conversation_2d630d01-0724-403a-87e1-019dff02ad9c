package com.dateup.android.utils

import android.view.View
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat
import androidx.core.view.updatePadding

/** Applies WindowInsets top to the provided view's padding. */
fun applyTopInsetPadding(view: View) {
    ViewCompat.setOnApplyWindowInsetsListener(view) { v, insets ->
        val systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars())
        v.updatePadding(top = systemBars.top)
        insets
    }
    ViewCompat.requestApplyInsets(view)
}

