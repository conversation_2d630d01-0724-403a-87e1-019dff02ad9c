package com.dateup.android.utils

import android.view.View
import android.view.ViewGroup
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat
import androidx.core.view.updatePadding

object EdgeToEdgeUtils {

    fun applyWindowInsets(view: View, applyTop: Boolean = true, applyBottom: Boolean = true) {
        ViewCompat.setOnApplyWindowInsetsListener(view) { v, insets ->
            val systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars())
            v.updatePadding(
                top = if (applyTop) systemBars.top else v.paddingTop,
                bottom = if (applyBottom) systemBars.bottom else v.paddingBottom
            )
            insets
        }
        ViewCompat.requestApplyInsets(view)
    }

    // Sets both top (status bar) and bottom = max(system bars, IME) in a single listener
    fun applySystemBarsAndImeInsets(root: View, applyTop: Boolean = true) {
        ViewCompat.setOnApplyWindowInsetsListener(root) { v, insets ->
            val systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars())
            val ime = insets.getInsets(WindowInsetsCompat.Type.ime())
            val bottom = maxOf(systemBars.bottom, ime.bottom)
            val top = if (applyTop) systemBars.top else v.paddingTop
            v.updatePadding(top = top, bottom = bottom)
            insets
        }
        ViewCompat.requestApplyInsets(root)
    }

    fun applyKeyboardInsetsToChildren(container: ViewGroup) {
        for (i in 0 until container.childCount) {
            val child = container.getChildAt(i)
            applyWindowInsets(child, applyTop = false, applyBottom = true)
        }
    }
}

